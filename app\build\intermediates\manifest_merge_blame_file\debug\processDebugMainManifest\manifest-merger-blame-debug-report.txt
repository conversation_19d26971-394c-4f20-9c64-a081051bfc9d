1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.gxzhaiwu"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:6:5-67
12-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:7:5-79
13-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:7:22-76
14
15    <permission
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.gxzhaiwu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.gxzhaiwu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:9:5-29:19
22        android:allowBackup="true"
22-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:12:9-54
28        android:icon="@mipmap/ic_launcher"
28-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:13:9-43
29        android:label="@string/app_name"
29-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:14:9-41
30        android:networkSecurityConfig="@xml/network_security_config"
30-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:18:9-69
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:15:9-54
32        android:supportsRtl="true"
32-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:16:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.GxZhaiWu" >
34-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:17:9-46
35        <activity
35-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:20:9-28:20
36            android:name="com.example.gxzhaiwu.MainActivity"
36-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:21:13-41
37            android:exported="true" >
37-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:22:13-36
38            <intent-filter>
38-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:23:13-27:29
39                <action android:name="android.intent.action.MAIN" />
39-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:24:17-69
39-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:24:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:26:17-77
41-->E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:26:27-74
42            </intent-filter>
43        </activity>
44
45        <provider
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
46            android:name="androidx.startup.InitializationProvider"
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
47            android:authorities="com.example.gxzhaiwu.androidx-startup"
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
48            android:exported="false" >
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
49            <meta-data
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.emoji2.text.EmojiCompatInitializer"
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
51                android:value="androidx.startup" />
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
53-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
54                android:value="androidx.startup" />
54-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
56-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
57                android:value="androidx.startup" />
57-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
58        </provider>
59
60        <receiver
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
61            android:name="androidx.profileinstaller.ProfileInstallReceiver"
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
62            android:directBootAware="false"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
63            android:enabled="true"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
64            android:exported="true"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
65            android:permission="android.permission.DUMP" >
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
66            <intent-filter>
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
67                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
68            </intent-filter>
69            <intent-filter>
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
70                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
71            </intent-filter>
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
73                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
76                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
77            </intent-filter>
78        </receiver>
79    </application>
80
81</manifest>
