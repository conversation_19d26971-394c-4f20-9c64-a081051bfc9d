// Generated by view binder compiler. Do not edit!
package com.example.gxzhaiwu.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.gxzhaiwu.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentLoginBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton btnLogin;

  @NonNull
  public final CardView cardError;

  @NonNull
  public final CardView cardLogin;

  @NonNull
  public final TextInputEditText etLogin;

  @NonNull
  public final TextInputEditText etPassword;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextInputLayout tilLogin;

  @NonNull
  public final TextInputLayout tilPassword;

  @NonNull
  public final TextView tvAppTitle;

  @NonNull
  public final TextView tvError;

  @NonNull
  public final TextView tvSubtitle;

  private FragmentLoginBinding(@NonNull ScrollView rootView, @NonNull MaterialButton btnLogin,
      @NonNull CardView cardError, @NonNull CardView cardLogin, @NonNull TextInputEditText etLogin,
      @NonNull TextInputEditText etPassword, @NonNull ImageView ivLogo,
      @NonNull ProgressBar progressBar, @NonNull TextInputLayout tilLogin,
      @NonNull TextInputLayout tilPassword, @NonNull TextView tvAppTitle, @NonNull TextView tvError,
      @NonNull TextView tvSubtitle) {
    this.rootView = rootView;
    this.btnLogin = btnLogin;
    this.cardError = cardError;
    this.cardLogin = cardLogin;
    this.etLogin = etLogin;
    this.etPassword = etPassword;
    this.ivLogo = ivLogo;
    this.progressBar = progressBar;
    this.tilLogin = tilLogin;
    this.tilPassword = tilPassword;
    this.tvAppTitle = tvAppTitle;
    this.tvError = tvError;
    this.tvSubtitle = tvSubtitle;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnLogin;
      MaterialButton btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.cardError;
      CardView cardError = ViewBindings.findChildViewById(rootView, id);
      if (cardError == null) {
        break missingId;
      }

      id = R.id.cardLogin;
      CardView cardLogin = ViewBindings.findChildViewById(rootView, id);
      if (cardLogin == null) {
        break missingId;
      }

      id = R.id.etLogin;
      TextInputEditText etLogin = ViewBindings.findChildViewById(rootView, id);
      if (etLogin == null) {
        break missingId;
      }

      id = R.id.etPassword;
      TextInputEditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.ivLogo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tilLogin;
      TextInputLayout tilLogin = ViewBindings.findChildViewById(rootView, id);
      if (tilLogin == null) {
        break missingId;
      }

      id = R.id.tilPassword;
      TextInputLayout tilPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilPassword == null) {
        break missingId;
      }

      id = R.id.tvAppTitle;
      TextView tvAppTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvAppTitle == null) {
        break missingId;
      }

      id = R.id.tvError;
      TextView tvError = ViewBindings.findChildViewById(rootView, id);
      if (tvError == null) {
        break missingId;
      }

      id = R.id.tvSubtitle;
      TextView tvSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (tvSubtitle == null) {
        break missingId;
      }

      return new FragmentLoginBinding((ScrollView) rootView, btnLogin, cardError, cardLogin,
          etLogin, etPassword, ivLogo, progressBar, tilLogin, tilPassword, tvAppTitle, tvError,
          tvSubtitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
