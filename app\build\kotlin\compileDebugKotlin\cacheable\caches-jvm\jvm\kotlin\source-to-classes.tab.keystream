6app/src/main/java/com/example/gxzhaiwu/MainActivity.kt=app/src/main/java/com/example/gxzhaiwu/data/api/ApiService.kt@app/src/main/java/com/example/gxzhaiwu/data/model/ApiResponse.ktAapp/src/main/java/com/example/gxzhaiwu/data/model/LoginRequest.ktBapp/src/main/java/com/example/gxzhaiwu/data/model/LoginResponse.kt9app/src/main/java/com/example/gxzhaiwu/data/model/User.ktHapp/src/main/java/com/example/gxzhaiwu/data/repository/AuthRepository.kt@app/src/main/java/com/example/gxzhaiwu/ui/login/LoginFragment.ktAapp/src/main/java/com/example/gxzhaiwu/ui/login/LoginViewModel.kt9app/src/main/java/com/example/gxzhaiwu/utils/Constants.kt=app/src/main/java/com/example/gxzhaiwu/utils/NetworkConfig.kt<app/src/main/java/com/example/gxzhaiwu/utils/NetworkUtils.ktBapp/src/main/java/com/example/gxzhaiwu/utils/PreferencesManager.kt{app/build/generated/data_binding_base_class_source_out/debug/out/com/example/gxzhaiwu/databinding/FragmentLoginBinding.javazapp/build/generated/data_binding_base_class_source_out/debug/out/com/example/gxzhaiwu/databinding/ActivityMainBinding.java                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                