<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 登录页面专用样式 -->
    <style name="LoginTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="boxStrokeWidth">2dp</item>
        <item name="hintTextColor">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="startIconTint">@color/primary</item>
        <item name="endIconTint">@color/primary</item>
    </style>

    <style name="LoginButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">@dimen/corner_radius_lg</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="rippleColor">@color/primary_light</item>
    </style>

    <style name="LoginCard" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">@dimen/corner_radius_xl</item>
        <item name="cardElevation">@dimen/elevation_md</item>
        <item name="cardBackgroundColor">@color/surface</item>
    </style>

    <style name="LoginTitle">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">28sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="LoginSubtitle">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:gravity">center</item>
    </style>
</resources>
