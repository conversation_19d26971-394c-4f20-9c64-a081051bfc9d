  Application android.app  Bundle android.app.Activity  
LoginFragment android.app.Activity  PreferencesManager android.app.Activity  R android.app.Activity  
ViewCompat android.app.Activity  WindowInsetsCompat android.app.Activity  enableEdgeToEdge android.app.Activity  findViewById android.app.Activity  onCreate android.app.Activity  setContentView android.app.Activity  showLoginFragment android.app.Activity  Context android.content  SharedPreferences android.content  Bundle android.content.Context  CONNECTIVITY_SERVICE android.content.Context  
LoginFragment android.content.Context  MODE_PRIVATE android.content.Context  PreferencesManager android.content.Context  R android.content.Context  
ViewCompat android.content.Context  WindowInsetsCompat android.content.Context  applicationContext android.content.Context  enableEdgeToEdge android.content.Context  findViewById android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  onCreate android.content.Context  setApplicationContext android.content.Context  setContentView android.content.Context  showLoginFragment android.content.Context  Bundle android.content.ContextWrapper  
LoginFragment android.content.ContextWrapper  PreferencesManager android.content.ContextWrapper  R android.content.ContextWrapper  
ViewCompat android.content.ContextWrapper  WindowInsetsCompat android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  findViewById android.content.ContextWrapper  onCreate android.content.ContextWrapper  setContentView android.content.ContextWrapper  showLoginFragment android.content.ContextWrapper  Editor !android.content.SharedPreferences  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getInt !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  remove (android.content.SharedPreferences.Editor  ConnectivityManager android.net  Network android.net  NetworkCapabilities android.net  NetworkInfo android.net  
activeNetwork android.net.ConnectivityManager  activeNetworkInfo android.net.ConnectivityManager  getACTIVENetwork android.net.ConnectivityManager  getACTIVENetworkInfo android.net.ConnectivityManager  getActiveNetwork android.net.ConnectivityManager  getActiveNetworkInfo android.net.ConnectivityManager  getNetworkCapabilities android.net.ConnectivityManager  setActiveNetwork android.net.ConnectivityManager  setActiveNetworkInfo android.net.ConnectivityManager  TRANSPORT_CELLULAR android.net.NetworkCapabilities  TRANSPORT_ETHERNET android.net.NetworkCapabilities  TRANSPORT_WIFI android.net.NetworkCapabilities  hasTransport android.net.NetworkCapabilities  getISConnected android.net.NetworkInfo  getIsConnected android.net.NetworkInfo  getTYPEName android.net.NetworkInfo  getTypeName android.net.NetworkInfo  isConnected android.net.NetworkInfo  setConnected android.net.NetworkInfo  setTypeName android.net.NetworkInfo  typeName android.net.NetworkInfo  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  equals android.os.Bundle  getTOString android.text.Editable  getToString android.text.Editable  toString android.text.Editable  Patterns android.util  
EMAIL_ADDRESS android.util.Patterns  LayoutInflater android.view  View android.view  	ViewGroup android.view  Bundle  android.view.ContextThemeWrapper  
LoginFragment  android.view.ContextThemeWrapper  PreferencesManager  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  
ViewCompat  android.view.ContextThemeWrapper  WindowInsetsCompat  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  findViewById  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  showLoginFragment  android.view.ContextThemeWrapper  GONE android.view.View  VISIBLE android.view.View  setOnClickListener android.view.View  
setPadding android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  
ScrollView android.widget  Toast android.widget  setOnClickListener android.widget.Button  
getVISIBILITY android.widget.ProgressBar  
getVisibility android.widget.ProgressBar  
setVisibility android.widget.ProgressBar  
visibility android.widget.ProgressBar  getTEXT android.widget.TextView  getText android.widget.TextView  
getVISIBILITY android.widget.TextView  
getVisibility android.widget.TextView  setOnClickListener android.widget.TextView  setText android.widget.TextView  
setVisibility android.widget.TextView  text android.widget.TextView  
visibility android.widget.TextView  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  
LoginFragment #androidx.activity.ComponentActivity  PreferencesManager #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  
ViewCompat #androidx.activity.ComponentActivity  WindowInsetsCompat #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  findViewById #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  showLoginFragment #androidx.activity.ComponentActivity  
LoginFragment -androidx.activity.ComponentActivity.Companion  PreferencesManager -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  
ViewCompat -androidx.activity.ComponentActivity.Companion  WindowInsetsCompat -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  AppCompatActivity androidx.appcompat.app  Bundle (androidx.appcompat.app.AppCompatActivity  
LoginFragment (androidx.appcompat.app.AppCompatActivity  PreferencesManager (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  
ViewCompat (androidx.appcompat.app.AppCompatActivity  WindowInsetsCompat (androidx.appcompat.app.AppCompatActivity  enableEdgeToEdge (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  showLoginFragment (androidx.appcompat.app.AppCompatActivity  setOnClickListener )androidx.appcompat.widget.AppCompatButton  Bundle #androidx.core.app.ComponentActivity  
LoginFragment #androidx.core.app.ComponentActivity  PreferencesManager #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  
ViewCompat #androidx.core.app.ComponentActivity  WindowInsetsCompat #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  findViewById #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  showLoginFragment #androidx.core.app.ComponentActivity  Insets androidx.core.graphics  bottom androidx.core.graphics.Insets  left androidx.core.graphics.Insets  right androidx.core.graphics.Insets  top androidx.core.graphics.Insets  
ViewCompat androidx.core.view  WindowInsetsCompat androidx.core.view  <SAM-CONSTRUCTOR> .androidx.core.view.OnApplyWindowInsetsListener  setOnApplyWindowInsetsListener androidx.core.view.ViewCompat  Type %androidx.core.view.WindowInsetsCompat  	getInsets %androidx.core.view.WindowInsetsCompat  
systemBars *androidx.core.view.WindowInsetsCompat.Type  Fragment androidx.fragment.app  FragmentManager androidx.fragment.app  
viewModels androidx.fragment.app  Boolean androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  FragmentLoginBinding androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  LoginViewModel androidx.fragment.app.Fragment  NetworkUtils androidx.fragment.app.Fragment  Observer androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  Toast androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  android androidx.fragment.app.Fragment  contains androidx.fragment.app.Fragment  	getString androidx.fragment.app.Fragment  getValue androidx.fragment.app.Fragment  	hideError androidx.fragment.app.Fragment  isEmpty androidx.fragment.app.Fragment  isValidEmail androidx.fragment.app.Fragment  let androidx.fragment.app.Fragment  observeViewModel androidx.fragment.app.Fragment  
onDestroyView androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  provideDelegate androidx.fragment.app.Fragment  requireContext androidx.fragment.app.Fragment  setupUI androidx.fragment.app.Fragment  	showError androidx.fragment.app.Fragment  toString androidx.fragment.app.Fragment  trim androidx.fragment.app.Fragment  
validateInput androidx.fragment.app.Fragment  
viewModels androidx.fragment.app.Fragment  Bundle &androidx.fragment.app.FragmentActivity  
LoginFragment &androidx.fragment.app.FragmentActivity  PreferencesManager &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  
ViewCompat &androidx.fragment.app.FragmentActivity  WindowInsetsCompat &androidx.fragment.app.FragmentActivity  enableEdgeToEdge &androidx.fragment.app.FragmentActivity  findViewById &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  showLoginFragment &androidx.fragment.app.FragmentActivity  beginTransaction %androidx.fragment.app.FragmentManager  commit )androidx.fragment.app.FragmentTransaction  replace )androidx.fragment.app.FragmentTransaction  AndroidViewModel androidx.lifecycle  LifecycleOwner androidx.lifecycle  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  Observer androidx.lifecycle  viewModelScope androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  AuthRepository #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  	Exception #androidx.lifecycle.AndroidViewModel  LiveData #androidx.lifecycle.AndroidViewModel  MutableLiveData #androidx.lifecycle.AndroidViewModel  PreferencesManager #androidx.lifecycle.AndroidViewModel  Result #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  
_isLoading #androidx.lifecycle.AndroidViewModel  _loginResult #androidx.lifecycle.AndroidViewModel  authRepository #androidx.lifecycle.AndroidViewModel  com #androidx.lifecycle.AndroidViewModel  contains #androidx.lifecycle.AndroidViewModel  launch #androidx.lifecycle.AndroidViewModel  login #androidx.lifecycle.AndroidViewModel  saveUserData #androidx.lifecycle.AndroidViewModel  viewModelScope #androidx.lifecycle.AndroidViewModel  observe androidx.lifecycle.LiveData  getVALUE "androidx.lifecycle.MutableLiveData  getValue "androidx.lifecycle.MutableLiveData  setValue "androidx.lifecycle.MutableLiveData  value "androidx.lifecycle.MutableLiveData  <SAM-CONSTRUCTOR> androidx.lifecycle.Observer  Application androidx.lifecycle.ViewModel  AuthRepository androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  LiveData androidx.lifecycle.ViewModel  MutableLiveData androidx.lifecycle.ViewModel  PreferencesManager androidx.lifecycle.ViewModel  Result androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  
_isLoading androidx.lifecycle.ViewModel  _loginResult androidx.lifecycle.ViewModel  authRepository androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  contains androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  login androidx.lifecycle.ViewModel  saveUserData androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  
LoginFragment com.example.gxzhaiwu  MainActivity com.example.gxzhaiwu  PreferencesManager com.example.gxzhaiwu  R com.example.gxzhaiwu  
ViewCompat com.example.gxzhaiwu  WindowInsetsCompat com.example.gxzhaiwu  enableEdgeToEdge com.example.gxzhaiwu  Bundle !com.example.gxzhaiwu.MainActivity  
LoginFragment !com.example.gxzhaiwu.MainActivity  PreferencesManager !com.example.gxzhaiwu.MainActivity  R !com.example.gxzhaiwu.MainActivity  
ViewCompat !com.example.gxzhaiwu.MainActivity  WindowInsetsCompat !com.example.gxzhaiwu.MainActivity  enableEdgeToEdge !com.example.gxzhaiwu.MainActivity  findViewById !com.example.gxzhaiwu.MainActivity  getENABLEEdgeToEdge !com.example.gxzhaiwu.MainActivity  getEnableEdgeToEdge !com.example.gxzhaiwu.MainActivity  getSUPPORTFragmentManager !com.example.gxzhaiwu.MainActivity  getSupportFragmentManager !com.example.gxzhaiwu.MainActivity  preferencesManager !com.example.gxzhaiwu.MainActivity  setContentView !com.example.gxzhaiwu.MainActivity  setSupportFragmentManager !com.example.gxzhaiwu.MainActivity  showLoginFragment !com.example.gxzhaiwu.MainActivity  supportFragmentManager !com.example.gxzhaiwu.MainActivity  id com.example.gxzhaiwu.R  layout com.example.gxzhaiwu.R  string com.example.gxzhaiwu.R  main com.example.gxzhaiwu.R.id  
activity_main com.example.gxzhaiwu.R.layout  	btn_login com.example.gxzhaiwu.R.string  error_empty_login com.example.gxzhaiwu.R.string  error_empty_password com.example.gxzhaiwu.R.string  error_invalid_email com.example.gxzhaiwu.R.string  error_login_failed com.example.gxzhaiwu.R.string  error_login_too_short com.example.gxzhaiwu.R.string  error_network_unavailable com.example.gxzhaiwu.R.string  error_password_too_short com.example.gxzhaiwu.R.string  
success_login com.example.gxzhaiwu.R.string  
ApiService com.example.gxzhaiwu.data.api  Body (com.example.gxzhaiwu.data.api.ApiService  LoginRequest (com.example.gxzhaiwu.data.api.ApiService  
LoginResponse (com.example.gxzhaiwu.data.api.ApiService  POST (com.example.gxzhaiwu.data.api.ApiService  Response (com.example.gxzhaiwu.data.api.ApiService  login (com.example.gxzhaiwu.data.api.ApiService  ApiErrorResponse com.example.gxzhaiwu.data.model  ApiResponse com.example.gxzhaiwu.data.model  Boolean com.example.gxzhaiwu.data.model  Int com.example.gxzhaiwu.data.model  List com.example.gxzhaiwu.data.model  	LoginData com.example.gxzhaiwu.data.model  LoginRequest com.example.gxzhaiwu.data.model  
LoginResponse com.example.gxzhaiwu.data.model  Map com.example.gxzhaiwu.data.model  String com.example.gxzhaiwu.data.model  User com.example.gxzhaiwu.data.model  Boolean 0com.example.gxzhaiwu.data.model.ApiErrorResponse  List 0com.example.gxzhaiwu.data.model.ApiErrorResponse  Map 0com.example.gxzhaiwu.data.model.ApiErrorResponse  String 0com.example.gxzhaiwu.data.model.ApiErrorResponse  Boolean +com.example.gxzhaiwu.data.model.ApiResponse  String +com.example.gxzhaiwu.data.model.ApiResponse  String )com.example.gxzhaiwu.data.model.LoginData  User )com.example.gxzhaiwu.data.model.LoginData  equals )com.example.gxzhaiwu.data.model.LoginData  token )com.example.gxzhaiwu.data.model.LoginData  user )com.example.gxzhaiwu.data.model.LoginData  String ,com.example.gxzhaiwu.data.model.LoginRequest  Boolean -com.example.gxzhaiwu.data.model.LoginResponse  	LoginData -com.example.gxzhaiwu.data.model.LoginResponse  String -com.example.gxzhaiwu.data.model.LoginResponse  data -com.example.gxzhaiwu.data.model.LoginResponse  message -com.example.gxzhaiwu.data.model.LoginResponse  success -com.example.gxzhaiwu.data.model.LoginResponse  Int $com.example.gxzhaiwu.data.model.User  List $com.example.gxzhaiwu.data.model.User  String $com.example.gxzhaiwu.data.model.User  email $com.example.gxzhaiwu.data.model.User  id $com.example.gxzhaiwu.data.model.User  name $com.example.gxzhaiwu.data.model.User  roles $com.example.gxzhaiwu.data.model.User  AuthRepository $com.example.gxzhaiwu.data.repository  LoginRequest $com.example.gxzhaiwu.data.repository  
NetworkConfig $com.example.gxzhaiwu.data.repository  String $com.example.gxzhaiwu.data.repository  
ApiService 3com.example.gxzhaiwu.data.repository.AuthRepository  LoginRequest 3com.example.gxzhaiwu.data.repository.AuthRepository  
LoginResponse 3com.example.gxzhaiwu.data.repository.AuthRepository  
NetworkConfig 3com.example.gxzhaiwu.data.repository.AuthRepository  Response 3com.example.gxzhaiwu.data.repository.AuthRepository  String 3com.example.gxzhaiwu.data.repository.AuthRepository  
apiService 3com.example.gxzhaiwu.data.repository.AuthRepository  login 3com.example.gxzhaiwu.data.repository.AuthRepository  FragmentLoginBinding  com.example.gxzhaiwu.databinding  btnLogin 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  etLogin 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  
etPassword 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  getROOT 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  getRoot 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  inflate 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  progressBar 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  root 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  setRoot 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  tilLogin 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  tilPassword 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  tvError 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  AuthRepository com.example.gxzhaiwu.ui.login  Boolean com.example.gxzhaiwu.ui.login  	Exception com.example.gxzhaiwu.ui.login  FragmentLoginBinding com.example.gxzhaiwu.ui.login  
LoginFragment com.example.gxzhaiwu.ui.login  LoginViewModel com.example.gxzhaiwu.ui.login  MutableLiveData com.example.gxzhaiwu.ui.login  NetworkUtils com.example.gxzhaiwu.ui.login  Observer com.example.gxzhaiwu.ui.login  PreferencesManager com.example.gxzhaiwu.ui.login  R com.example.gxzhaiwu.ui.login  Result com.example.gxzhaiwu.ui.login  String com.example.gxzhaiwu.ui.login  Toast com.example.gxzhaiwu.ui.login  View com.example.gxzhaiwu.ui.login  
_isLoading com.example.gxzhaiwu.ui.login  _loginResult com.example.gxzhaiwu.ui.login  android com.example.gxzhaiwu.ui.login  authRepository com.example.gxzhaiwu.ui.login  com com.example.gxzhaiwu.ui.login  contains com.example.gxzhaiwu.ui.login  getValue com.example.gxzhaiwu.ui.login  isEmpty com.example.gxzhaiwu.ui.login  launch com.example.gxzhaiwu.ui.login  let com.example.gxzhaiwu.ui.login  provideDelegate com.example.gxzhaiwu.ui.login  saveUserData com.example.gxzhaiwu.ui.login  toString com.example.gxzhaiwu.ui.login  trim com.example.gxzhaiwu.ui.login  viewModelScope com.example.gxzhaiwu.ui.login  
viewModels com.example.gxzhaiwu.ui.login  Boolean +com.example.gxzhaiwu.ui.login.LoginFragment  Bundle +com.example.gxzhaiwu.ui.login.LoginFragment  FragmentLoginBinding +com.example.gxzhaiwu.ui.login.LoginFragment  LayoutInflater +com.example.gxzhaiwu.ui.login.LoginFragment  LoginViewModel +com.example.gxzhaiwu.ui.login.LoginFragment  NetworkUtils +com.example.gxzhaiwu.ui.login.LoginFragment  Observer +com.example.gxzhaiwu.ui.login.LoginFragment  R +com.example.gxzhaiwu.ui.login.LoginFragment  String +com.example.gxzhaiwu.ui.login.LoginFragment  Toast +com.example.gxzhaiwu.ui.login.LoginFragment  View +com.example.gxzhaiwu.ui.login.LoginFragment  	ViewGroup +com.example.gxzhaiwu.ui.login.LoginFragment  _binding +com.example.gxzhaiwu.ui.login.LoginFragment  android +com.example.gxzhaiwu.ui.login.LoginFragment  binding +com.example.gxzhaiwu.ui.login.LoginFragment  contains +com.example.gxzhaiwu.ui.login.LoginFragment  context +com.example.gxzhaiwu.ui.login.LoginFragment  
getANDROID +com.example.gxzhaiwu.ui.login.LoginFragment  
getAndroid +com.example.gxzhaiwu.ui.login.LoginFragment  getCONTAINS +com.example.gxzhaiwu.ui.login.LoginFragment  
getCONTEXT +com.example.gxzhaiwu.ui.login.LoginFragment  getContains +com.example.gxzhaiwu.ui.login.LoginFragment  
getContext +com.example.gxzhaiwu.ui.login.LoginFragment  getGETValue +com.example.gxzhaiwu.ui.login.LoginFragment  getGetValue +com.example.gxzhaiwu.ui.login.LoginFragment  
getISEmpty +com.example.gxzhaiwu.ui.login.LoginFragment  
getIsEmpty +com.example.gxzhaiwu.ui.login.LoginFragment  getLET +com.example.gxzhaiwu.ui.login.LoginFragment  getLet +com.example.gxzhaiwu.ui.login.LoginFragment  getPROVIDEDelegate +com.example.gxzhaiwu.ui.login.LoginFragment  getProvideDelegate +com.example.gxzhaiwu.ui.login.LoginFragment  	getString +com.example.gxzhaiwu.ui.login.LoginFragment  getTOString +com.example.gxzhaiwu.ui.login.LoginFragment  getTRIM +com.example.gxzhaiwu.ui.login.LoginFragment  getToString +com.example.gxzhaiwu.ui.login.LoginFragment  getTrim +com.example.gxzhaiwu.ui.login.LoginFragment  getVIEWLifecycleOwner +com.example.gxzhaiwu.ui.login.LoginFragment  
getVIEWModels +com.example.gxzhaiwu.ui.login.LoginFragment  getValue +com.example.gxzhaiwu.ui.login.LoginFragment  getViewLifecycleOwner +com.example.gxzhaiwu.ui.login.LoginFragment  
getViewModels +com.example.gxzhaiwu.ui.login.LoginFragment  	hideError +com.example.gxzhaiwu.ui.login.LoginFragment  isEmpty +com.example.gxzhaiwu.ui.login.LoginFragment  isValidEmail +com.example.gxzhaiwu.ui.login.LoginFragment  let +com.example.gxzhaiwu.ui.login.LoginFragment  observeViewModel +com.example.gxzhaiwu.ui.login.LoginFragment  provideDelegate +com.example.gxzhaiwu.ui.login.LoginFragment  requireContext +com.example.gxzhaiwu.ui.login.LoginFragment  
setContext +com.example.gxzhaiwu.ui.login.LoginFragment  setViewLifecycleOwner +com.example.gxzhaiwu.ui.login.LoginFragment  setupUI +com.example.gxzhaiwu.ui.login.LoginFragment  	showError +com.example.gxzhaiwu.ui.login.LoginFragment  toString +com.example.gxzhaiwu.ui.login.LoginFragment  trim +com.example.gxzhaiwu.ui.login.LoginFragment  
validateInput +com.example.gxzhaiwu.ui.login.LoginFragment  viewLifecycleOwner +com.example.gxzhaiwu.ui.login.LoginFragment  	viewModel +com.example.gxzhaiwu.ui.login.LoginFragment  
viewModels +com.example.gxzhaiwu.ui.login.LoginFragment  Application ,com.example.gxzhaiwu.ui.login.LoginViewModel  AuthRepository ,com.example.gxzhaiwu.ui.login.LoginViewModel  Boolean ,com.example.gxzhaiwu.ui.login.LoginViewModel  	Exception ,com.example.gxzhaiwu.ui.login.LoginViewModel  LiveData ,com.example.gxzhaiwu.ui.login.LoginViewModel  MutableLiveData ,com.example.gxzhaiwu.ui.login.LoginViewModel  PreferencesManager ,com.example.gxzhaiwu.ui.login.LoginViewModel  Result ,com.example.gxzhaiwu.ui.login.LoginViewModel  String ,com.example.gxzhaiwu.ui.login.LoginViewModel  
_errorMessage ,com.example.gxzhaiwu.ui.login.LoginViewModel  
_isLoading ,com.example.gxzhaiwu.ui.login.LoginViewModel  _loginResult ,com.example.gxzhaiwu.ui.login.LoginViewModel  authRepository ,com.example.gxzhaiwu.ui.login.LoginViewModel  com ,com.example.gxzhaiwu.ui.login.LoginViewModel  contains ,com.example.gxzhaiwu.ui.login.LoginViewModel  errorMessage ,com.example.gxzhaiwu.ui.login.LoginViewModel  getCONTAINS ,com.example.gxzhaiwu.ui.login.LoginViewModel  getContains ,com.example.gxzhaiwu.ui.login.LoginViewModel  	getLAUNCH ,com.example.gxzhaiwu.ui.login.LoginViewModel  	getLaunch ,com.example.gxzhaiwu.ui.login.LoginViewModel  getVIEWModelScope ,com.example.gxzhaiwu.ui.login.LoginViewModel  getViewModelScope ,com.example.gxzhaiwu.ui.login.LoginViewModel  	isLoading ,com.example.gxzhaiwu.ui.login.LoginViewModel  launch ,com.example.gxzhaiwu.ui.login.LoginViewModel  login ,com.example.gxzhaiwu.ui.login.LoginViewModel  loginResult ,com.example.gxzhaiwu.ui.login.LoginViewModel  preferencesManager ,com.example.gxzhaiwu.ui.login.LoginViewModel  saveUserData ,com.example.gxzhaiwu.ui.login.LoginViewModel  viewModelScope ,com.example.gxzhaiwu.ui.login.LoginViewModel  
ApiService com.example.gxzhaiwu.utils  Boolean com.example.gxzhaiwu.utils  Build com.example.gxzhaiwu.utils  	Constants com.example.gxzhaiwu.utils  Context com.example.gxzhaiwu.utils  Gson com.example.gxzhaiwu.utils  GsonBuilder com.example.gxzhaiwu.utils  GsonConverterFactory com.example.gxzhaiwu.utils  HttpLoggingInterceptor com.example.gxzhaiwu.utils  Int com.example.gxzhaiwu.utils  List com.example.gxzhaiwu.utils  NetworkCapabilities com.example.gxzhaiwu.utils  
NetworkConfig com.example.gxzhaiwu.utils  NetworkUtils com.example.gxzhaiwu.utils  OkHttpClient com.example.gxzhaiwu.utils  PreferencesManager com.example.gxzhaiwu.utils  Retrofit com.example.gxzhaiwu.utils  String com.example.gxzhaiwu.utils  Suppress com.example.gxzhaiwu.utils  TimeUnit com.example.gxzhaiwu.utils  Volatile com.example.gxzhaiwu.utils  also com.example.gxzhaiwu.utils  apply com.example.gxzhaiwu.utils  	emptyList com.example.gxzhaiwu.utils  getValue com.example.gxzhaiwu.utils  invoke com.example.gxzhaiwu.utils  
isNullOrEmpty com.example.gxzhaiwu.utils  java com.example.gxzhaiwu.utils  lazy com.example.gxzhaiwu.utils  provideDelegate com.example.gxzhaiwu.utils  synchronized com.example.gxzhaiwu.utils  BASE_URL $com.example.gxzhaiwu.utils.Constants  CONNECT_TIMEOUT $com.example.gxzhaiwu.utils.Constants  KEY_IS_LOGGED_IN $com.example.gxzhaiwu.utils.Constants  	KEY_TOKEN $com.example.gxzhaiwu.utils.Constants  KEY_USER_EMAIL $com.example.gxzhaiwu.utils.Constants  KEY_USER_ID $com.example.gxzhaiwu.utils.Constants  
KEY_USER_NAME $com.example.gxzhaiwu.utils.Constants  KEY_USER_ROLES $com.example.gxzhaiwu.utils.Constants  
PREFS_NAME $com.example.gxzhaiwu.utils.Constants  READ_TIMEOUT $com.example.gxzhaiwu.utils.Constants  
WRITE_TIMEOUT $com.example.gxzhaiwu.utils.Constants  
ApiService (com.example.gxzhaiwu.utils.NetworkConfig  	Constants (com.example.gxzhaiwu.utils.NetworkConfig  GsonBuilder (com.example.gxzhaiwu.utils.NetworkConfig  GsonConverterFactory (com.example.gxzhaiwu.utils.NetworkConfig  HttpLoggingInterceptor (com.example.gxzhaiwu.utils.NetworkConfig  OkHttpClient (com.example.gxzhaiwu.utils.NetworkConfig  Retrofit (com.example.gxzhaiwu.utils.NetworkConfig  TimeUnit (com.example.gxzhaiwu.utils.NetworkConfig  
apiService (com.example.gxzhaiwu.utils.NetworkConfig  apply (com.example.gxzhaiwu.utils.NetworkConfig  createOkHttpClient (com.example.gxzhaiwu.utils.NetworkConfig  createRetrofit (com.example.gxzhaiwu.utils.NetworkConfig  getAPPLY (com.example.gxzhaiwu.utils.NetworkConfig  getApply (com.example.gxzhaiwu.utils.NetworkConfig  getGETValue (com.example.gxzhaiwu.utils.NetworkConfig  getGetValue (com.example.gxzhaiwu.utils.NetworkConfig  getLAZY (com.example.gxzhaiwu.utils.NetworkConfig  getLazy (com.example.gxzhaiwu.utils.NetworkConfig  getPROVIDEDelegate (com.example.gxzhaiwu.utils.NetworkConfig  getProvideDelegate (com.example.gxzhaiwu.utils.NetworkConfig  getValue (com.example.gxzhaiwu.utils.NetworkConfig  java (com.example.gxzhaiwu.utils.NetworkConfig  lazy (com.example.gxzhaiwu.utils.NetworkConfig  provideDelegate (com.example.gxzhaiwu.utils.NetworkConfig  Boolean 'com.example.gxzhaiwu.utils.NetworkUtils  Build 'com.example.gxzhaiwu.utils.NetworkUtils  ConnectivityManager 'com.example.gxzhaiwu.utils.NetworkUtils  Context 'com.example.gxzhaiwu.utils.NetworkUtils  NetworkCapabilities 'com.example.gxzhaiwu.utils.NetworkUtils  String 'com.example.gxzhaiwu.utils.NetworkUtils  Suppress 'com.example.gxzhaiwu.utils.NetworkUtils  isNetworkAvailable 'com.example.gxzhaiwu.utils.NetworkUtils  Boolean -com.example.gxzhaiwu.utils.PreferencesManager  	Companion -com.example.gxzhaiwu.utils.PreferencesManager  	Constants -com.example.gxzhaiwu.utils.PreferencesManager  Context -com.example.gxzhaiwu.utils.PreferencesManager  Gson -com.example.gxzhaiwu.utils.PreferencesManager  Int -com.example.gxzhaiwu.utils.PreferencesManager  List -com.example.gxzhaiwu.utils.PreferencesManager  PreferencesManager -com.example.gxzhaiwu.utils.PreferencesManager  SharedPreferences -com.example.gxzhaiwu.utils.PreferencesManager  String -com.example.gxzhaiwu.utils.PreferencesManager  	TypeToken -com.example.gxzhaiwu.utils.PreferencesManager  User -com.example.gxzhaiwu.utils.PreferencesManager  Volatile -com.example.gxzhaiwu.utils.PreferencesManager  also -com.example.gxzhaiwu.utils.PreferencesManager  	emptyList -com.example.gxzhaiwu.utils.PreferencesManager  getALSO -com.example.gxzhaiwu.utils.PreferencesManager  getAlso -com.example.gxzhaiwu.utils.PreferencesManager  getEMPTYList -com.example.gxzhaiwu.utils.PreferencesManager  getEmptyList -com.example.gxzhaiwu.utils.PreferencesManager  getISNullOrEmpty -com.example.gxzhaiwu.utils.PreferencesManager  getInstance -com.example.gxzhaiwu.utils.PreferencesManager  getIsNullOrEmpty -com.example.gxzhaiwu.utils.PreferencesManager  getToken -com.example.gxzhaiwu.utils.PreferencesManager  gson -com.example.gxzhaiwu.utils.PreferencesManager  invoke -com.example.gxzhaiwu.utils.PreferencesManager  
isLoggedIn -com.example.gxzhaiwu.utils.PreferencesManager  
isNullOrEmpty -com.example.gxzhaiwu.utils.PreferencesManager  saveUserData -com.example.gxzhaiwu.utils.PreferencesManager  sharedPreferences -com.example.gxzhaiwu.utils.PreferencesManager  synchronized -com.example.gxzhaiwu.utils.PreferencesManager  Boolean 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  	Constants 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  Context 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  Gson 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  INSTANCE 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  Int 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  List 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  PreferencesManager 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  SharedPreferences 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  String 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  	TypeToken 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  User 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  Volatile 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  also 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  	emptyList 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  getALSO 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  getAlso 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  getEMPTYList 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  getEmptyList 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  getISNullOrEmpty 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  getInstance 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  getIsNullOrEmpty 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  getSYNCHRONIZED 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  getSynchronized 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  invoke 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  
isNullOrEmpty 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  synchronized 7com.example.gxzhaiwu.utils.PreferencesManager.Companion  getTYPE Mcom.example.gxzhaiwu.utils.PreferencesManager.getUserRoles.<no name provided>  getType Mcom.example.gxzhaiwu.utils.PreferencesManager.getUserRoles.<no name provided>  setType Mcom.example.gxzhaiwu.utils.PreferencesManager.getUserRoles.<no name provided>  getISEnabled 1com.google.android.material.button.MaterialButton  getIsEnabled 1com.google.android.material.button.MaterialButton  getTEXT 1com.google.android.material.button.MaterialButton  getText 1com.google.android.material.button.MaterialButton  	isEnabled 1com.google.android.material.button.MaterialButton  
setEnabled 1com.google.android.material.button.MaterialButton  setOnClickListener 1com.google.android.material.button.MaterialButton  setText 1com.google.android.material.button.MaterialButton  text 1com.google.android.material.button.MaterialButton  getTEXT 7com.google.android.material.textfield.TextInputEditText  getText 7com.google.android.material.textfield.TextInputEditText  setText 7com.google.android.material.textfield.TextInputEditText  text 7com.google.android.material.textfield.TextInputEditText  error 5com.google.android.material.textfield.TextInputLayout  getERROR 5com.google.android.material.textfield.TextInputLayout  getError 5com.google.android.material.textfield.TextInputLayout  setError 5com.google.android.material.textfield.TextInputLayout  Gson com.google.gson  GsonBuilder com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  create com.google.gson.GsonBuilder  
setLenient com.google.gson.GsonBuilder  	TypeToken com.google.gson.reflect  
ApiService 	java.lang  AuthRepository 	java.lang  Build 	java.lang  Class 	java.lang  	Constants 	java.lang  Context 	java.lang  	Exception 	java.lang  FragmentLoginBinding 	java.lang  Gson 	java.lang  GsonBuilder 	java.lang  GsonConverterFactory 	java.lang  HttpLoggingInterceptor 	java.lang  
LoginFragment 	java.lang  LoginRequest 	java.lang  MutableLiveData 	java.lang  NetworkCapabilities 	java.lang  
NetworkConfig 	java.lang  NetworkUtils 	java.lang  Observer 	java.lang  OkHttpClient 	java.lang  PreferencesManager 	java.lang  R 	java.lang  Result 	java.lang  Retrofit 	java.lang  TimeUnit 	java.lang  Toast 	java.lang  View 	java.lang  
ViewCompat 	java.lang  WindowInsetsCompat 	java.lang  
_isLoading 	java.lang  _loginResult 	java.lang  also 	java.lang  android 	java.lang  apply 	java.lang  authRepository 	java.lang  com 	java.lang  contains 	java.lang  	emptyList 	java.lang  getValue 	java.lang  isEmpty 	java.lang  
isNullOrEmpty 	java.lang  java 	java.lang  launch 	java.lang  lazy 	java.lang  let 	java.lang  provideDelegate 	java.lang  saveUserData 	java.lang  synchronized 	java.lang  toString 	java.lang  trim 	java.lang  message java.lang.Exception  Type java.lang.reflect  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  matches java.util.regex.Matcher  matcher java.util.regex.Pattern  Any kotlin  
ApiService kotlin  AuthRepository kotlin  Boolean kotlin  Build kotlin  CharSequence kotlin  	Constants kotlin  Context kotlin  	Exception kotlin  FragmentLoginBinding kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Gson kotlin  GsonBuilder kotlin  GsonConverterFactory kotlin  HttpLoggingInterceptor kotlin  Int kotlin  Lazy kotlin  
LoginFragment kotlin  LoginRequest kotlin  Long kotlin  MutableLiveData kotlin  NetworkCapabilities kotlin  
NetworkConfig kotlin  NetworkUtils kotlin  Nothing kotlin  Observer kotlin  OkHttpClient kotlin  PreferencesManager kotlin  R kotlin  Result kotlin  Retrofit kotlin  String kotlin  Suppress kotlin  TimeUnit kotlin  Toast kotlin  Unit kotlin  View kotlin  
ViewCompat kotlin  Volatile kotlin  WindowInsetsCompat kotlin  
_isLoading kotlin  _loginResult kotlin  also kotlin  android kotlin  apply kotlin  authRepository kotlin  com kotlin  contains kotlin  	emptyList kotlin  getValue kotlin  isEmpty kotlin  
isNullOrEmpty kotlin  java kotlin  launch kotlin  lazy kotlin  let kotlin  provideDelegate kotlin  saveUserData kotlin  synchronized kotlin  toString kotlin  trim kotlin  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  exceptionOrNull 
kotlin.Result  failure 
kotlin.Result  getLET 
kotlin.Result  getLet 
kotlin.Result  	isSuccess 
kotlin.Result  let 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  getCONTAINS 
kotlin.String  getContains 
kotlin.String  
getISEmpty 
kotlin.String  getISNullOrEmpty 
kotlin.String  
getIsEmpty 
kotlin.String  getIsNullOrEmpty 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  getTRIM 
kotlin.String  getTrim 
kotlin.String  isEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  
ApiService kotlin.annotation  AuthRepository kotlin.annotation  Build kotlin.annotation  	Constants kotlin.annotation  Context kotlin.annotation  	Exception kotlin.annotation  FragmentLoginBinding kotlin.annotation  Gson kotlin.annotation  GsonBuilder kotlin.annotation  GsonConverterFactory kotlin.annotation  HttpLoggingInterceptor kotlin.annotation  
LoginFragment kotlin.annotation  LoginRequest kotlin.annotation  MutableLiveData kotlin.annotation  NetworkCapabilities kotlin.annotation  
NetworkConfig kotlin.annotation  NetworkUtils kotlin.annotation  Observer kotlin.annotation  OkHttpClient kotlin.annotation  PreferencesManager kotlin.annotation  R kotlin.annotation  Result kotlin.annotation  Retrofit kotlin.annotation  TimeUnit kotlin.annotation  Toast kotlin.annotation  View kotlin.annotation  
ViewCompat kotlin.annotation  Volatile kotlin.annotation  WindowInsetsCompat kotlin.annotation  
_isLoading kotlin.annotation  _loginResult kotlin.annotation  also kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  authRepository kotlin.annotation  com kotlin.annotation  contains kotlin.annotation  	emptyList kotlin.annotation  getValue kotlin.annotation  isEmpty kotlin.annotation  
isNullOrEmpty kotlin.annotation  java kotlin.annotation  launch kotlin.annotation  lazy kotlin.annotation  let kotlin.annotation  provideDelegate kotlin.annotation  saveUserData kotlin.annotation  synchronized kotlin.annotation  toString kotlin.annotation  trim kotlin.annotation  
ApiService kotlin.collections  AuthRepository kotlin.collections  Build kotlin.collections  	Constants kotlin.collections  Context kotlin.collections  	Exception kotlin.collections  FragmentLoginBinding kotlin.collections  Gson kotlin.collections  GsonBuilder kotlin.collections  GsonConverterFactory kotlin.collections  HttpLoggingInterceptor kotlin.collections  List kotlin.collections  
LoginFragment kotlin.collections  LoginRequest kotlin.collections  Map kotlin.collections  MutableLiveData kotlin.collections  NetworkCapabilities kotlin.collections  
NetworkConfig kotlin.collections  NetworkUtils kotlin.collections  Observer kotlin.collections  OkHttpClient kotlin.collections  PreferencesManager kotlin.collections  R kotlin.collections  Result kotlin.collections  Retrofit kotlin.collections  TimeUnit kotlin.collections  Toast kotlin.collections  View kotlin.collections  
ViewCompat kotlin.collections  Volatile kotlin.collections  WindowInsetsCompat kotlin.collections  
_isLoading kotlin.collections  _loginResult kotlin.collections  also kotlin.collections  android kotlin.collections  apply kotlin.collections  authRepository kotlin.collections  com kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  getValue kotlin.collections  isEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  java kotlin.collections  launch kotlin.collections  lazy kotlin.collections  let kotlin.collections  provideDelegate kotlin.collections  saveUserData kotlin.collections  synchronized kotlin.collections  toString kotlin.collections  trim kotlin.collections  
ApiService kotlin.comparisons  AuthRepository kotlin.comparisons  Build kotlin.comparisons  	Constants kotlin.comparisons  Context kotlin.comparisons  	Exception kotlin.comparisons  FragmentLoginBinding kotlin.comparisons  Gson kotlin.comparisons  GsonBuilder kotlin.comparisons  GsonConverterFactory kotlin.comparisons  HttpLoggingInterceptor kotlin.comparisons  
LoginFragment kotlin.comparisons  LoginRequest kotlin.comparisons  MutableLiveData kotlin.comparisons  NetworkCapabilities kotlin.comparisons  
NetworkConfig kotlin.comparisons  NetworkUtils kotlin.comparisons  Observer kotlin.comparisons  OkHttpClient kotlin.comparisons  PreferencesManager kotlin.comparisons  R kotlin.comparisons  Result kotlin.comparisons  Retrofit kotlin.comparisons  TimeUnit kotlin.comparisons  Toast kotlin.comparisons  View kotlin.comparisons  
ViewCompat kotlin.comparisons  Volatile kotlin.comparisons  WindowInsetsCompat kotlin.comparisons  
_isLoading kotlin.comparisons  _loginResult kotlin.comparisons  also kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  authRepository kotlin.comparisons  com kotlin.comparisons  contains kotlin.comparisons  	emptyList kotlin.comparisons  getValue kotlin.comparisons  isEmpty kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  java kotlin.comparisons  launch kotlin.comparisons  lazy kotlin.comparisons  let kotlin.comparisons  provideDelegate kotlin.comparisons  saveUserData kotlin.comparisons  synchronized kotlin.comparisons  toString kotlin.comparisons  trim kotlin.comparisons  SuspendFunction1 kotlin.coroutines  
ApiService 	kotlin.io  AuthRepository 	kotlin.io  Build 	kotlin.io  	Constants 	kotlin.io  Context 	kotlin.io  	Exception 	kotlin.io  FragmentLoginBinding 	kotlin.io  Gson 	kotlin.io  GsonBuilder 	kotlin.io  GsonConverterFactory 	kotlin.io  HttpLoggingInterceptor 	kotlin.io  
LoginFragment 	kotlin.io  LoginRequest 	kotlin.io  MutableLiveData 	kotlin.io  NetworkCapabilities 	kotlin.io  
NetworkConfig 	kotlin.io  NetworkUtils 	kotlin.io  Observer 	kotlin.io  OkHttpClient 	kotlin.io  PreferencesManager 	kotlin.io  R 	kotlin.io  Result 	kotlin.io  Retrofit 	kotlin.io  TimeUnit 	kotlin.io  Toast 	kotlin.io  View 	kotlin.io  
ViewCompat 	kotlin.io  Volatile 	kotlin.io  WindowInsetsCompat 	kotlin.io  
_isLoading 	kotlin.io  _loginResult 	kotlin.io  also 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  authRepository 	kotlin.io  com 	kotlin.io  contains 	kotlin.io  	emptyList 	kotlin.io  getValue 	kotlin.io  isEmpty 	kotlin.io  
isNullOrEmpty 	kotlin.io  java 	kotlin.io  launch 	kotlin.io  lazy 	kotlin.io  let 	kotlin.io  provideDelegate 	kotlin.io  saveUserData 	kotlin.io  synchronized 	kotlin.io  toString 	kotlin.io  trim 	kotlin.io  
ApiService 
kotlin.jvm  AuthRepository 
kotlin.jvm  Build 
kotlin.jvm  	Constants 
kotlin.jvm  Context 
kotlin.jvm  	Exception 
kotlin.jvm  FragmentLoginBinding 
kotlin.jvm  Gson 
kotlin.jvm  GsonBuilder 
kotlin.jvm  GsonConverterFactory 
kotlin.jvm  HttpLoggingInterceptor 
kotlin.jvm  
LoginFragment 
kotlin.jvm  LoginRequest 
kotlin.jvm  MutableLiveData 
kotlin.jvm  NetworkCapabilities 
kotlin.jvm  
NetworkConfig 
kotlin.jvm  NetworkUtils 
kotlin.jvm  Observer 
kotlin.jvm  OkHttpClient 
kotlin.jvm  PreferencesManager 
kotlin.jvm  R 
kotlin.jvm  Result 
kotlin.jvm  Retrofit 
kotlin.jvm  TimeUnit 
kotlin.jvm  Toast 
kotlin.jvm  View 
kotlin.jvm  
ViewCompat 
kotlin.jvm  Volatile 
kotlin.jvm  WindowInsetsCompat 
kotlin.jvm  
_isLoading 
kotlin.jvm  _loginResult 
kotlin.jvm  also 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  authRepository 
kotlin.jvm  com 
kotlin.jvm  contains 
kotlin.jvm  	emptyList 
kotlin.jvm  getValue 
kotlin.jvm  isEmpty 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  java 
kotlin.jvm  launch 
kotlin.jvm  lazy 
kotlin.jvm  let 
kotlin.jvm  provideDelegate 
kotlin.jvm  saveUserData 
kotlin.jvm  synchronized 
kotlin.jvm  toString 
kotlin.jvm  trim 
kotlin.jvm  
ApiService 
kotlin.ranges  AuthRepository 
kotlin.ranges  Build 
kotlin.ranges  	Constants 
kotlin.ranges  Context 
kotlin.ranges  	Exception 
kotlin.ranges  FragmentLoginBinding 
kotlin.ranges  Gson 
kotlin.ranges  GsonBuilder 
kotlin.ranges  GsonConverterFactory 
kotlin.ranges  HttpLoggingInterceptor 
kotlin.ranges  
LoginFragment 
kotlin.ranges  LoginRequest 
kotlin.ranges  MutableLiveData 
kotlin.ranges  NetworkCapabilities 
kotlin.ranges  
NetworkConfig 
kotlin.ranges  NetworkUtils 
kotlin.ranges  Observer 
kotlin.ranges  OkHttpClient 
kotlin.ranges  PreferencesManager 
kotlin.ranges  R 
kotlin.ranges  Result 
kotlin.ranges  Retrofit 
kotlin.ranges  TimeUnit 
kotlin.ranges  Toast 
kotlin.ranges  View 
kotlin.ranges  
ViewCompat 
kotlin.ranges  Volatile 
kotlin.ranges  WindowInsetsCompat 
kotlin.ranges  
_isLoading 
kotlin.ranges  _loginResult 
kotlin.ranges  also 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  authRepository 
kotlin.ranges  com 
kotlin.ranges  contains 
kotlin.ranges  	emptyList 
kotlin.ranges  getValue 
kotlin.ranges  isEmpty 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  java 
kotlin.ranges  launch 
kotlin.ranges  lazy 
kotlin.ranges  let 
kotlin.ranges  provideDelegate 
kotlin.ranges  saveUserData 
kotlin.ranges  synchronized 
kotlin.ranges  toString 
kotlin.ranges  trim 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  
ApiService kotlin.sequences  AuthRepository kotlin.sequences  Build kotlin.sequences  	Constants kotlin.sequences  Context kotlin.sequences  	Exception kotlin.sequences  FragmentLoginBinding kotlin.sequences  Gson kotlin.sequences  GsonBuilder kotlin.sequences  GsonConverterFactory kotlin.sequences  HttpLoggingInterceptor kotlin.sequences  
LoginFragment kotlin.sequences  LoginRequest kotlin.sequences  MutableLiveData kotlin.sequences  NetworkCapabilities kotlin.sequences  
NetworkConfig kotlin.sequences  NetworkUtils kotlin.sequences  Observer kotlin.sequences  OkHttpClient kotlin.sequences  PreferencesManager kotlin.sequences  R kotlin.sequences  Result kotlin.sequences  Retrofit kotlin.sequences  TimeUnit kotlin.sequences  Toast kotlin.sequences  View kotlin.sequences  
ViewCompat kotlin.sequences  Volatile kotlin.sequences  WindowInsetsCompat kotlin.sequences  
_isLoading kotlin.sequences  _loginResult kotlin.sequences  also kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  authRepository kotlin.sequences  com kotlin.sequences  contains kotlin.sequences  	emptyList kotlin.sequences  getValue kotlin.sequences  isEmpty kotlin.sequences  
isNullOrEmpty kotlin.sequences  java kotlin.sequences  launch kotlin.sequences  lazy kotlin.sequences  let kotlin.sequences  provideDelegate kotlin.sequences  saveUserData kotlin.sequences  synchronized kotlin.sequences  toString kotlin.sequences  trim kotlin.sequences  
ApiService kotlin.text  AuthRepository kotlin.text  Build kotlin.text  	Constants kotlin.text  Context kotlin.text  	Exception kotlin.text  FragmentLoginBinding kotlin.text  Gson kotlin.text  GsonBuilder kotlin.text  GsonConverterFactory kotlin.text  HttpLoggingInterceptor kotlin.text  
LoginFragment kotlin.text  LoginRequest kotlin.text  MutableLiveData kotlin.text  NetworkCapabilities kotlin.text  
NetworkConfig kotlin.text  NetworkUtils kotlin.text  Observer kotlin.text  OkHttpClient kotlin.text  PreferencesManager kotlin.text  R kotlin.text  Result kotlin.text  Retrofit kotlin.text  TimeUnit kotlin.text  Toast kotlin.text  View kotlin.text  
ViewCompat kotlin.text  Volatile kotlin.text  WindowInsetsCompat kotlin.text  
_isLoading kotlin.text  _loginResult kotlin.text  also kotlin.text  android kotlin.text  apply kotlin.text  authRepository kotlin.text  com kotlin.text  contains kotlin.text  	emptyList kotlin.text  getValue kotlin.text  isEmpty kotlin.text  
isNullOrEmpty kotlin.text  java kotlin.text  launch kotlin.text  lazy kotlin.text  let kotlin.text  provideDelegate kotlin.text  saveUserData kotlin.text  synchronized kotlin.text  toString kotlin.text  trim kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  	Exception !kotlinx.coroutines.CoroutineScope  Result !kotlinx.coroutines.CoroutineScope  
_isLoading !kotlinx.coroutines.CoroutineScope  _loginResult !kotlinx.coroutines.CoroutineScope  authRepository !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  getAUTHRepository !kotlinx.coroutines.CoroutineScope  getAuthRepository !kotlinx.coroutines.CoroutineScope  getCONTAINS !kotlinx.coroutines.CoroutineScope  getContains !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getSAVEUserData !kotlinx.coroutines.CoroutineScope  getSaveUserData !kotlinx.coroutines.CoroutineScope  
get_isLoading !kotlinx.coroutines.CoroutineScope  get_loginResult !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  saveUserData !kotlinx.coroutines.CoroutineScope  OkHttpClient okhttp3  Builder okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.OkHttpClient.Companion  HttpLoggingInterceptor okhttp3.logging  HttpLoggingInterceptor &okhttp3.logging.HttpLoggingInterceptor  Level &okhttp3.logging.HttpLoggingInterceptor  apply &okhttp3.logging.HttpLoggingInterceptor  getAPPLY &okhttp3.logging.HttpLoggingInterceptor  getApply &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  BODY ,okhttp3.logging.HttpLoggingInterceptor.Level  Response 	retrofit2  Retrofit 	retrofit2  body retrofit2.Response  code retrofit2.Response  getISSuccessful retrofit2.Response  getIsSuccessful retrofit2.Response  isSuccessful retrofit2.Response  
setSuccessful retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  Body retrofit2.http  POST retrofit2.http  	cardError 5com.example.gxzhaiwu.databinding.FragmentLoginBinding  
getVISIBILITY !androidx.cardview.widget.CardView  
getVisibility !androidx.cardview.widget.CardView  
setVisibility !androidx.cardview.widget.CardView  
visibility !androidx.cardview.widget.CardView                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    