package com.example.gxzhaiwu.utils

import android.content.Context
import android.content.SharedPreferences
import com.example.gxzhaiwu.data.model.User
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * SharedPreferences管理工具类
 */
class PreferencesManager private constructor(context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(Constants.PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    companion object {
        @Volatile
        private var INSTANCE: PreferencesManager? = null
        
        fun getInstance(context: Context): PreferencesManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PreferencesManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 保存用户登录信息
     */
    fun saveUserData(user: User, token: String) {
        val editor = sharedPreferences.edit()
        editor.putString(Constants.KEY_TOKEN, token)
        editor.putInt(Constants.KEY_USER_ID, user.id)
        editor.putString(Constants.KEY_USER_NAME, user.name)
        editor.putString(Constants.KEY_USER_EMAIL, user.email)
        editor.putString(Constants.KEY_USER_ROLES, gson.toJson(user.roles))
        editor.putBoolean(Constants.KEY_IS_LOGGED_IN, true)
        editor.apply()
    }
    
    /**
     * 获取访问令牌
     */
    fun getToken(): String? {
        return sharedPreferences.getString(Constants.KEY_TOKEN, null)
    }
    
    /**
     * 获取用户ID
     */
    fun getUserId(): Int {
        return sharedPreferences.getInt(Constants.KEY_USER_ID, -1)
    }
    
    /**
     * 获取用户姓名
     */
    fun getUserName(): String? {
        return sharedPreferences.getString(Constants.KEY_USER_NAME, null)
    }
    
    /**
     * 获取用户邮箱
     */
    fun getUserEmail(): String? {
        return sharedPreferences.getString(Constants.KEY_USER_EMAIL, null)
    }
    
    /**
     * 获取用户角色列表
     */
    fun getUserRoles(): List<String> {
        val rolesJson = sharedPreferences.getString(Constants.KEY_USER_ROLES, null)
        return if (rolesJson != null) {
            val type = object : TypeToken<List<String>>() {}.type
            gson.fromJson(rolesJson, type)
        } else {
            emptyList()
        }
    }
    
    /**
     * 检查用户是否已登录
     */
    fun isLoggedIn(): Boolean {
        return sharedPreferences.getBoolean(Constants.KEY_IS_LOGGED_IN, false) && 
               !getToken().isNullOrEmpty()
    }
    
    /**
     * 清除用户登录信息（登出）
     */
    fun clearUserData() {
        val editor = sharedPreferences.edit()
        editor.remove(Constants.KEY_TOKEN)
        editor.remove(Constants.KEY_USER_ID)
        editor.remove(Constants.KEY_USER_NAME)
        editor.remove(Constants.KEY_USER_EMAIL)
        editor.remove(Constants.KEY_USER_ROLES)
        editor.putBoolean(Constants.KEY_IS_LOGGED_IN, false)
        editor.apply()
    }
}
