package com.example.gxzhaiwu.utils

/**
 * 应用常量定义
 */
object Constants {
    // API相关常量
    const val BASE_URL = "http://www.laravelqk.com/api/"
    const val CONNECT_TIMEOUT = 30L
    const val READ_TIMEOUT = 30L
    const val WRITE_TIMEOUT = 30L
    
    // SharedPreferences相关常量
    const val PREFS_NAME = "gx_zhaiwu_prefs"
    const val KEY_TOKEN = "auth_token"
    const val KEY_USER_ID = "user_id"
    const val KEY_USER_NAME = "user_name"
    const val KEY_USER_EMAIL = "user_email"
    const val KEY_USER_ROLES = "user_roles"
    const val KEY_IS_LOGGED_IN = "is_logged_in"
}
