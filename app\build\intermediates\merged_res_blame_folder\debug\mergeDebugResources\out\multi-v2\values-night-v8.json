{"logs": [{"outputFile": "com.example.gxzhaiwu.app-mergeDebugResources-38:/values-night-v8/values-night-v8.xml", "map": [{"source": "E:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "5", "endColumns": "12", "endOffsets": "315"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9f721d637f67d4db61062e1122557cba\\transformed\\appcompat-1.7.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "6,7,8,9,10,11,12,39", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "171,241,325,409,505,607,709,3691", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "236,320,404,500,602,704,798,3775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\54318c60d4cc5aa50171bceead402775\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "803,878,989,1078,1179,1286,1393,1492,1599,1702,1829,1917,2041,2143,2245,2361,2463,2577,2705,2821,2943,3079,3199,3333,3453,3565,3780,3897,4021,4151,4273,4411,4545,4661", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "873,984,1073,1174,1281,1388,1487,1594,1697,1824,1912,2036,2138,2240,2356,2458,2572,2700,2816,2938,3074,3194,3328,3448,3560,3686,3892,4016,4146,4268,4406,4540,4656,4776"}}]}]}