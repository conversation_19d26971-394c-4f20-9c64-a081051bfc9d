package com.example.gxzhaiwu.data.api

import com.example.gxzhaiwu.data.model.LoginRequest
import com.example.gxzhaiwu.data.model.LoginResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * API服务接口定义
 */
interface ApiService {
    
    /**
     * 用户登录
     * @param loginRequest 登录请求参数
     * @return 登录响应
     */
    @POST("login")
    suspend fun login(@Body loginRequest: LoginRequest): Response<LoginResponse>
}
