package com.example.gxzhaiwu.data.repository

import com.example.gxzhaiwu.data.api.ApiService
import com.example.gxzhaiwu.data.model.LoginRequest
import com.example.gxzhaiwu.data.model.LoginResponse
import com.example.gxzhaiwu.utils.NetworkConfig
import retrofit2.Response

/**
 * 认证相关数据仓库
 */
class AuthRepository {
    
    private val apiService: ApiService = NetworkConfig.apiService
    
    /**
     * 用户登录
     * @param login 用户名或邮箱
     * @param password 密码
     * @return 登录响应
     */
    suspend fun login(login: String, password: String): Response<LoginResponse> {
        val loginRequest = LoginRequest(login, password)
        return apiService.login(loginRequest)
    }
}
