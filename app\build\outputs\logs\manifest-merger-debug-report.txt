-- Merging decision tree log ---
manifest
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:2:1-31:12
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:2:1-31:12
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:2:1-31:12
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:2:1-31:12
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9f427bd5231d76b5d766d27ade05f42\transformed\viewbinding-8.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54318c60d4cc5aa50171bceead402775\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\742b224b1ef878671380ce7f235a61a4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55ee5af25d66465cec5a05d819fbef88\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f721d637f67d4db61062e1122557cba\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348729850a0d4bacdfae26cdd5ddd0ea\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5fdc35ba7631241579d1f59ecc5dda1\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20f7b7ae6f261dfe49726a9752d5f8c6\transformed\fragment-1.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d9ce0fa6245aefdeb5f4e60fb8c602\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\855dfd765e06bd06ae771bc6f40af2b7\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e84f0e4e579c686597262725eb98b4c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0671848ad1c59b6a7f0e0e83047810a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\551986e6eb7ee3b5e8df3f160232c4d5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5195410c3cf2dbbbae00f2b5a34174a\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\973331ad2dd14746a6ea47461a26a3b9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6364aff5de03e6f07ffb4d2eba1779d2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000719695eaecf9871643e4901bd8285\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0162c8b08f18f0e1b26a8c226bf9188\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f798b912fe9b337c65fbf590b42cd244\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1d62b22b150f9c394f9e8b90c5af62\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8df0e8dad4ea9b321d46eefed138b6ed\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aa3160768b87ea72f6ef68a45facae0\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74f2fc4e0bb445a2747ab70181a4abe7\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1e54a77f18ada14fca3d374b61b5dc3\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cef75d31322d289c379cbd41c9bbc62\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67525d2d14d1fc59c26e5ceb9b9ba9ba\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50c4feef7e4cdbde5af2ceaf3eed3c16\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05115ac455dd24002d7fd9ab8dbb4b88\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fd4bf816718029b54d0b55c289294d8\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86f7afc04dad4ca6fab653054eb313e8\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3f23a63c32af32262a968ef8d6d5170\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73322e5127178d86670506a221dfca11\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1742713bf3a946dbbd29df1729d261ec\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e088dd9c275ccec325549fbb2b000ab\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ece981faba28eb65df5a6c6f026277e6\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c533422cbbf9c49fb13c18418235aab\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b19242d939c3bd17654713e8531259f4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a14821ab9bee3e17b67e73ff763c9aa8\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00007bc8391c367684773ec2257209d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7df652eab8424724f812e04d2aa3dbb0\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e922977026ed7f1322d5762e08b86e51\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06e55e3346de1d63e0e5998c0dc64f6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39af621e71142d6ca3e808d8021915d7\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9b1a067c6e308fce40ea4bf610d28b3\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c616d15e87c11217d347b9f1a0979dfb\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc0e0f1d72acce21ef98b14b44a35fb8\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21bdbb8c8fc9d6663ba7bbeb7a0fd97d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:7:22-76
application
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:9:5-29:19
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:9:5-29:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54318c60d4cc5aa50171bceead402775\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54318c60d4cc5aa50171bceead402775\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\742b224b1ef878671380ce7f235a61a4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\742b224b1ef878671380ce7f235a61a4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00007bc8391c367684773ec2257209d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00007bc8391c367684773ec2257209d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06e55e3346de1d63e0e5998c0dc64f6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06e55e3346de1d63e0e5998c0dc64f6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:14:9-41
	android:fullBackupContent
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:12:9-54
	android:roundIcon
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:15:9-54
	tools:targetApi
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:13:9-43
	android:allowBackup
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:10:9-35
	android:theme
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:17:9-46
	android:networkSecurityConfig
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:18:9-69
	android:dataExtractionRules
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:11:9-65
activity#com.example.gxzhaiwu.MainActivity
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:20:9-28:20
	android:exported
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:22:13-36
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:21:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:23:13-27:29
action#android.intent.action.MAIN
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:24:17-69
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:24:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:26:17-77
	android:name
		ADDED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml:26:27-74
uses-sdk
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9f427bd5231d76b5d766d27ade05f42\transformed\viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9f427bd5231d76b5d766d27ade05f42\transformed\viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54318c60d4cc5aa50171bceead402775\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54318c60d4cc5aa50171bceead402775\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\742b224b1ef878671380ce7f235a61a4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\742b224b1ef878671380ce7f235a61a4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55ee5af25d66465cec5a05d819fbef88\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55ee5af25d66465cec5a05d819fbef88\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f721d637f67d4db61062e1122557cba\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f721d637f67d4db61062e1122557cba\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348729850a0d4bacdfae26cdd5ddd0ea\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348729850a0d4bacdfae26cdd5ddd0ea\transformed\fragment-ktx-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5fdc35ba7631241579d1f59ecc5dda1\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5fdc35ba7631241579d1f59ecc5dda1\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20f7b7ae6f261dfe49726a9752d5f8c6\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20f7b7ae6f261dfe49726a9752d5f8c6\transformed\fragment-1.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d9ce0fa6245aefdeb5f4e60fb8c602\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d9ce0fa6245aefdeb5f4e60fb8c602\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\855dfd765e06bd06ae771bc6f40af2b7\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\855dfd765e06bd06ae771bc6f40af2b7\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e84f0e4e579c686597262725eb98b4c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e84f0e4e579c686597262725eb98b4c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0671848ad1c59b6a7f0e0e83047810a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0671848ad1c59b6a7f0e0e83047810a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\551986e6eb7ee3b5e8df3f160232c4d5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\551986e6eb7ee3b5e8df3f160232c4d5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5195410c3cf2dbbbae00f2b5a34174a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5195410c3cf2dbbbae00f2b5a34174a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\973331ad2dd14746a6ea47461a26a3b9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\973331ad2dd14746a6ea47461a26a3b9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6364aff5de03e6f07ffb4d2eba1779d2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6364aff5de03e6f07ffb4d2eba1779d2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000719695eaecf9871643e4901bd8285\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\000719695eaecf9871643e4901bd8285\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0162c8b08f18f0e1b26a8c226bf9188\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0162c8b08f18f0e1b26a8c226bf9188\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f798b912fe9b337c65fbf590b42cd244\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f798b912fe9b337c65fbf590b42cd244\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1d62b22b150f9c394f9e8b90c5af62\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1d62b22b150f9c394f9e8b90c5af62\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8df0e8dad4ea9b321d46eefed138b6ed\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8df0e8dad4ea9b321d46eefed138b6ed\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aa3160768b87ea72f6ef68a45facae0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aa3160768b87ea72f6ef68a45facae0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74f2fc4e0bb445a2747ab70181a4abe7\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74f2fc4e0bb445a2747ab70181a4abe7\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1e54a77f18ada14fca3d374b61b5dc3\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1e54a77f18ada14fca3d374b61b5dc3\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cef75d31322d289c379cbd41c9bbc62\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cef75d31322d289c379cbd41c9bbc62\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67525d2d14d1fc59c26e5ceb9b9ba9ba\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67525d2d14d1fc59c26e5ceb9b9ba9ba\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50c4feef7e4cdbde5af2ceaf3eed3c16\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50c4feef7e4cdbde5af2ceaf3eed3c16\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05115ac455dd24002d7fd9ab8dbb4b88\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05115ac455dd24002d7fd9ab8dbb4b88\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fd4bf816718029b54d0b55c289294d8\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fd4bf816718029b54d0b55c289294d8\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86f7afc04dad4ca6fab653054eb313e8\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86f7afc04dad4ca6fab653054eb313e8\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3f23a63c32af32262a968ef8d6d5170\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3f23a63c32af32262a968ef8d6d5170\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73322e5127178d86670506a221dfca11\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73322e5127178d86670506a221dfca11\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1742713bf3a946dbbd29df1729d261ec\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1742713bf3a946dbbd29df1729d261ec\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e088dd9c275ccec325549fbb2b000ab\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e088dd9c275ccec325549fbb2b000ab\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ece981faba28eb65df5a6c6f026277e6\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ece981faba28eb65df5a6c6f026277e6\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c533422cbbf9c49fb13c18418235aab\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c533422cbbf9c49fb13c18418235aab\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b19242d939c3bd17654713e8531259f4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b19242d939c3bd17654713e8531259f4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a14821ab9bee3e17b67e73ff763c9aa8\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a14821ab9bee3e17b67e73ff763c9aa8\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00007bc8391c367684773ec2257209d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00007bc8391c367684773ec2257209d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7df652eab8424724f812e04d2aa3dbb0\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7df652eab8424724f812e04d2aa3dbb0\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e922977026ed7f1322d5762e08b86e51\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e922977026ed7f1322d5762e08b86e51\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06e55e3346de1d63e0e5998c0dc64f6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06e55e3346de1d63e0e5998c0dc64f6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39af621e71142d6ca3e808d8021915d7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39af621e71142d6ca3e808d8021915d7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9b1a067c6e308fce40ea4bf610d28b3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9b1a067c6e308fce40ea4bf610d28b3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c616d15e87c11217d347b9f1a0979dfb\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c616d15e87c11217d347b9f1a0979dfb\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc0e0f1d72acce21ef98b14b44a35fb8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc0e0f1d72acce21ef98b14b44a35fb8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21bdbb8c8fc9d6663ba7bbeb7a0fd97d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21bdbb8c8fc9d6663ba7bbeb7a0fd97d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00007bc8391c367684773ec2257209d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00007bc8391c367684773ec2257209d1\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d5e291ecf62b30110fdf85e60641090\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.gxzhaiwu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.gxzhaiwu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\deb0beff519b9c4f8f9fad521546f280\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488720e356bb0d185856ff260682f64c\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9224ff4f54237bf0e96b7d108d0cf806\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
