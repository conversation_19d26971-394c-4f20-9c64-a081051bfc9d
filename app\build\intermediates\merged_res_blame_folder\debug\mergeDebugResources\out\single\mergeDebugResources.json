[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\layout_fragment_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\layout\\fragment_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\xml\\backup_rules.xml"}, {"merged": "com.example.gxzhaiwu.app-debug-40:/layout_fragment_login.xml.flat", "source": "com.example.gxzhaiwu.app-main-42:/layout/fragment_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\drawable_bg_gradient_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\drawable\\bg_gradient_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\drawable_bg_button_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\drawable\\bg_button_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\drawable_bg_login_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\drawable\\bg_login_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-debug-40:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.gxzhaiwu.app-main-42:\\drawable\\ic_launcher_foreground.xml"}]