#Sat Aug 02 20:50:07 CST 2025
com.example.gxzhaiwu.app-main-42\:/drawable/bg_button_primary.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_button_primary.xml.flat
com.example.gxzhaiwu.app-main-42\:/drawable/bg_gradient_primary.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_gradient_primary.xml.flat
com.example.gxzhaiwu.app-main-42\:/drawable/bg_login_card.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_login_card.xml.flat
com.example.gxzhaiwu.app-main-42\:/drawable/ic_launcher_background.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.gxzhaiwu.app-main-42\:/drawable/ic_launcher_foreground.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-anydpi/ic_launcher.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-anydpi/ic_launcher_round.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-hdpi/ic_launcher.webp=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-hdpi/ic_launcher_round.webp=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-mdpi/ic_launcher.webp=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-mdpi/ic_launcher_round.webp=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-xhdpi/ic_launcher.webp=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-xhdpi/ic_launcher_round.webp=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-xxhdpi/ic_launcher.webp=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-xxhdpi/ic_launcher_round.webp=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-xxxhdpi/ic_launcher.webp=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.gxzhaiwu.app-main-42\:/mipmap-xxxhdpi/ic_launcher_round.webp=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.gxzhaiwu.app-main-42\:/xml/backup_rules.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.gxzhaiwu.app-main-42\:/xml/data_extraction_rules.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.gxzhaiwu.app-main-42\:/xml/network_security_config.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_network_security_config.xml.flat
com.example.gxzhaiwu.app-mergeDebugResources-39\:/layout/activity_main.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.gxzhaiwu.app-mergeDebugResources-39\:/layout/fragment_login.xml=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_login.xml.flat
