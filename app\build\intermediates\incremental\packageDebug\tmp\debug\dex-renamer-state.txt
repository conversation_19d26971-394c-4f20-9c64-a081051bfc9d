#Sat Aug 02 20:54:52 CST 2025
base.0=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.2=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\12\\classes.dex
base.3=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\13\\classes.dex
base.4=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\2\\classes.dex
base.5=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
base.6=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\9\\classes.dex
base.7=E\:\\Tools\\Android\\AndroidStudioProjects\\GxZhaiWu\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=12/classes.dex
path.3=13/classes.dex
path.4=2/classes.dex
path.5=6/classes.dex
path.6=9/classes.dex
path.7=classes2.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
