<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res"><file name="ic_launcher_background" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_login" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\layout\fragment_login.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_dark">#1976D2</color><color name="accent">#FF4081</color><color name="surface">#FFFFFF</color><color name="background_primary">#F5F5F5</color><color name="primary">#2196F3</color><color name="text_hint">#BDBDBD</color><color name="primary_light">#BBDEFB</color><color name="surface_variant">#F8F9FA</color><color name="warning">#FF9800</color><color name="text_secondary">#757575</color><color name="text_error">#F44336</color><color name="info">#2196F3</color><color name="outline">#E0E0E0</color><color name="error">#F44336</color><color name="background_secondary">#FFFFFF</color><color name="text_primary">#212121</color><color name="background_gradient_end">#FFFFFF</color><color name="background_gradient_start">#E3F2FD</color><color name="success">#4CAF50</color></file><file path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">债务管理系统</string><string name="login_title">债务管理系统</string><string name="login_subtitle">请登录您的账户</string><string name="hint_login">用户名或邮箱</string><string name="hint_password">密码</string><string name="btn_login">登录</string><string name="error_empty_login">请输入用户名或邮箱</string><string name="error_empty_password">请输入密码</string><string name="error_invalid_email">请输入有效的邮箱地址</string><string name="error_password_too_short">密码长度不能少于6位</string><string name="error_login_too_short">用户名长度不能少于2位</string><string name="error_network_unavailable">网络连接不可用，请检查网络设置</string><string name="error_login_failed">登录失败，请重试</string><string name="success_login">登录成功，欢迎使用！</string></file><file path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.GxZhaiWu" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.GxZhaiWu" parent="Base.Theme.GxZhaiWu"/></file><file path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.GxZhaiWu" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="bg_button_primary" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\drawable\bg_button_primary.xml" qualifiers="" type="drawable"/><file name="bg_gradient_primary" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\drawable\bg_gradient_primary.xml" qualifiers="" type="drawable"/><file name="bg_login_card" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\drawable\bg_login_card.xml" qualifiers="" type="drawable"/><file path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="spacing_xs">4dp</dimen><dimen name="spacing_sm">8dp</dimen><dimen name="spacing_md">16dp</dimen><dimen name="spacing_lg">24dp</dimen><dimen name="spacing_xl">32dp</dimen><dimen name="spacing_xxl">48dp</dimen><dimen name="corner_radius_sm">4dp</dimen><dimen name="corner_radius_md">8dp</dimen><dimen name="corner_radius_lg">12dp</dimen><dimen name="corner_radius_xl">16dp</dimen><dimen name="elevation_sm">2dp</dimen><dimen name="elevation_md">4dp</dimen><dimen name="elevation_lg">8dp</dimen><dimen name="login_card_margin">24dp</dimen><dimen name="login_card_padding">32dp</dimen><dimen name="login_logo_size">100dp</dimen><dimen name="login_button_height">56dp</dimen><dimen name="login_input_spacing">20dp</dimen></file><file path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\values\styles.xml" qualifiers=""><style name="LoginTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="boxStrokeWidth">2dp</item>
        <item name="hintTextColor">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="startIconTint">@color/primary</item>
        <item name="endIconTint">@color/primary</item>
    </style><style name="LoginButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">@dimen/corner_radius_lg</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="rippleColor">@color/primary_light</item>
    </style><style name="LoginCard" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">@dimen/corner_radius_xl</item>
        <item name="cardElevation">@dimen/elevation_md</item>
        <item name="cardBackgroundColor">@color/surface</item>
    </style><style name="LoginTitle">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">28sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
    </style><style name="LoginSubtitle">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:gravity">center</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>