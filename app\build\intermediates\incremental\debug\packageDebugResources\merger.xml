<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res"><file name="ic_launcher_background" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_login" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\layout\fragment_login.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">债务管理系统</string><string name="login_title">债务管理系统</string><string name="login_subtitle">请登录您的账户</string><string name="hint_login">用户名或邮箱</string><string name="hint_password">密码</string><string name="btn_login">登录</string><string name="error_empty_login">请输入用户名或邮箱</string><string name="error_empty_password">请输入密码</string><string name="error_invalid_email">请输入有效的邮箱地址</string><string name="error_password_too_short">密码长度不能少于6位</string><string name="error_login_too_short">用户名长度不能少于2位</string><string name="error_network_unavailable">网络连接不可用，请检查网络设置</string><string name="error_login_failed">登录失败，请重试</string><string name="success_login">登录成功，欢迎使用！</string></file><file path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.GxZhaiWu" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.GxZhaiWu" parent="Base.Theme.GxZhaiWu"/></file><file path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.GxZhaiWu" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Tools\Android\AndroidStudioProjects\GxZhaiWu\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>