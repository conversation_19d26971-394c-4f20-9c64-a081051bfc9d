<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/primary_dark" />
            <corners android:radius="@dimen/corner_radius_lg" />
        </shape>
    </item>
    
    <!-- 禁用状态 -->
    <item android:state_enabled="false">
        <shape>
            <solid android:color="@color/text_hint" />
            <corners android:radius="@dimen/corner_radius_lg" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape>
            <solid android:color="@color/primary" />
            <corners android:radius="@dimen/corner_radius_lg" />
        </shape>
    </item>
</selector>
