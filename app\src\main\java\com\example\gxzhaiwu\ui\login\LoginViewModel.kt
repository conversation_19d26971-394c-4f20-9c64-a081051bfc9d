package com.example.gxzhaiwu.ui.login

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.gxzhaiwu.data.repository.AuthRepository
import com.example.gxzhaiwu.utils.PreferencesManager
import kotlinx.coroutines.launch

/**
 * 登录界面ViewModel
 */
class LoginViewModel(application: Application) : AndroidViewModel(application) {

    private val authRepository = AuthRepository()
    private val preferencesManager = PreferencesManager.getInstance(application)
    
    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 登录结果
    private val _loginResult = MutableLiveData<Result<String>>()
    val loginResult: LiveData<Result<String>> = _loginResult
    
    // 错误信息
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage
    
    /**
     * 执行登录
     */
    fun login(login: String, password: String) {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                val response = authRepository.login(login, password)
                
                if (response.isSuccessful) {
                    val loginResponse = response.body()
                    if (loginResponse?.success == true && loginResponse.data != null) {
                        // 登录成功，保存用户信息
                        val userData = loginResponse.data
                        saveUserData(userData.user, userData.token)
                        _loginResult.value = Result.success("登录成功")
                    } else {
                        // API返回失败
                        _loginResult.value = Result.failure(
                            Exception(loginResponse?.message ?: "登录失败")
                        )
                    }
                } else {
                    // HTTP请求失败
                    val errorMessage = when (response.code()) {
                        401 -> "用户名或密码错误"
                        422 -> "请求参数错误"
                        500 -> "服务器内部错误"
                        else -> "网络请求失败 (${response.code()})"
                    }
                    _loginResult.value = Result.failure(Exception(errorMessage))
                }
            } catch (e: Exception) {
                // 网络异常或其他异常
                val errorMessage = when {
                    e.message?.contains("timeout") == true -> "网络连接超时"
                    e.message?.contains("Unable to resolve host") == true -> "网络连接失败"
                    else -> "登录失败: ${e.message}"
                }
                _loginResult.value = Result.failure(Exception(errorMessage))
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 保存用户数据到本地
     */
    private fun saveUserData(user: com.example.gxzhaiwu.data.model.User, token: String) {
        preferencesManager.saveUserData(user, token)
    }
}
