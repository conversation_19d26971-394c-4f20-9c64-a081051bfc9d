!com.example.gxzhaiwu.MainActivity(com.example.gxzhaiwu.data.api.ApiService+com.example.gxzhaiwu.data.model.ApiResponse0com.example.gxzhaiwu.data.model.ApiErrorResponse,com.example.gxzhaiwu.data.model.LoginRequest-com.example.gxzhaiwu.data.model.LoginResponse)com.example.gxzhaiwu.data.model.LoginData$com.example.gxzhaiwu.data.model.User3com.example.gxzhaiwu.data.repository.AuthRepository+com.example.gxzhaiwu.ui.login.LoginFragment,com.example.gxzhaiwu.ui.login.LoginViewModel$com.example.gxzhaiwu.utils.Constants(com.example.gxzhaiwu.utils.NetworkConfig'com.example.gxzhaiwu.utils.NetworkUtils-com.example.gxzhaiwu.utils.PreferencesManager7com.example.gxzhaiwu.utils.PreferencesManager.Companion5com.example.gxzhaiwu.databinding.FragmentLoginBinding4com.example.gxzhaiwu.databinding.ActivityMainBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          