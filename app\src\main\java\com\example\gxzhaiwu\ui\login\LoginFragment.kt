package com.example.gxzhaiwu.ui.login

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import com.example.gxzhaiwu.databinding.FragmentLoginBinding
import com.example.gxzhaiwu.utils.NetworkUtils

/**
 * 登录界面Fragment
 */
class LoginFragment : Fragment() {
    
    private var _binding: FragmentLoginBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: LoginViewModel by viewModels()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentLoginBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
        observeViewModel()
    }
    
    /**
     * 设置UI交互
     */
    private fun setupUI() {
        binding.btnLogin.setOnClickListener {
            val login = binding.etLogin.text.toString().trim()
            val password = binding.etPassword.text.toString().trim()

            if (validateInput(login, password)) {
                // 检查网络连接
                if (!NetworkUtils.isNetworkAvailable(requireContext())) {
                    showError(getString(R.string.error_network_unavailable))
                    return@setOnClickListener
                }

                viewModel.login(login, password)
            }
        }
    }
    
    /**
     * 观察ViewModel状态变化
     */
    private fun observeViewModel() {
        // 观察加载状态
        viewModel.isLoading.observe(viewLifecycleOwner, Observer { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnLogin.isEnabled = !isLoading
            binding.btnLogin.text = if (isLoading) "" else getString(R.string.btn_login)
        })
        
        // 观察登录结果
        viewModel.loginResult.observe(viewLifecycleOwner, Observer { result ->
            result?.let {
                if (it.isSuccess) {
                    // 登录成功
                    hideError()
                    Toast.makeText(context, getString(R.string.success_login), Toast.LENGTH_SHORT).show()
                    // TODO: 跳转到主界面
                    // 可以在这里添加页面跳转逻辑
                } else {
                    // 登录失败
                    showError(it.exceptionOrNull()?.message ?: getString(R.string.error_login_failed))
                }
            }
        })
        
        // 观察错误信息
        viewModel.errorMessage.observe(viewLifecycleOwner, Observer { errorMessage ->
            errorMessage?.let {
                showError(it)
            }
        })
    }
    
    /**
     * 验证输入
     */
    private fun validateInput(login: String, password: String): Boolean {
        // 清除之前的错误信息
        binding.tilLogin.error = null
        binding.tilPassword.error = null
        hideError()

        var isValid = true

        if (login.isEmpty()) {
            binding.tilLogin.error = getString(R.string.error_empty_login)
            isValid = false
        } else if (login.length < 2) {
            binding.tilLogin.error = getString(R.string.error_login_too_short)
            isValid = false
        }

        if (password.isEmpty()) {
            binding.tilPassword.error = getString(R.string.error_empty_password)
            isValid = false
        } else if (password.length < 6) {
            binding.tilPassword.error = getString(R.string.error_password_too_short)
            isValid = false
        }

        // 如果是邮箱格式，进行邮箱格式验证
        if (login.contains("@") && !isValidEmail(login)) {
            binding.tilLogin.error = getString(R.string.error_invalid_email)
            isValid = false
        }

        return isValid
    }

    /**
     * 验证邮箱格式
     */
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
    
    /**
     * 显示错误信息
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }
    
    /**
     * 隐藏错误信息
     */
    private fun hideError() {
        binding.tvError.visibility = View.GONE
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
