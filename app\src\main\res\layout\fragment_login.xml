<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient_primary"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="match_parent"
        android:padding="@dimen/spacing_lg">

        <!-- 顶部Logo区域 -->
        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="@dimen/login_logo_size"
            android:layout_height="@dimen/login_logo_size"
            android:layout_marginTop="@dimen/spacing_xxl"
            android:src="@mipmap/ic_launcher"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvAppTitle"
            style="@style/LoginTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_md"
            android:text="@string/login_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivLogo" />

        <TextView
            android:id="@+id/tvSubtitle"
            style="@style/LoginSubtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_sm"
            android:text="@string/login_subtitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvAppTitle" />

        <!-- 登录卡片容器 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardLogin"
            style="@style/LoginCard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/login_card_margin"
            android:layout_marginTop="@dimen/spacing_xxl"
            android:layout_marginEnd="@dimen/login_card_margin"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvSubtitle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/login_card_padding">

                <!-- 用户名输入框 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilLogin"
                    style="@style/LoginTextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/hint_login"
                    app:boxCornerRadiusBottomEnd="@dimen/corner_radius_md"
                    app:boxCornerRadiusBottomStart="@dimen/corner_radius_md"
                    app:boxCornerRadiusTopEnd="@dimen/corner_radius_md"
                    app:boxCornerRadiusTopStart="@dimen/corner_radius_md"
                    app:startIconDrawable="@android:drawable/ic_dialog_email">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etLogin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textEmailAddress"
                        android:maxLines="1"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 密码输入框 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPassword"
                    style="@style/LoginTextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/login_input_spacing"
                    android:hint="@string/hint_password"
                    app:boxCornerRadiusBottomEnd="@dimen/corner_radius_md"
                    app:boxCornerRadiusBottomStart="@dimen/corner_radius_md"
                    app:boxCornerRadiusTopEnd="@dimen/corner_radius_md"
                    app:boxCornerRadiusTopStart="@dimen/corner_radius_md"
                    app:endIconMode="password_toggle"
                    app:startIconDrawable="@android:drawable/ic_lock_lock">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 登录按钮 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnLogin"
                    style="@style/LoginButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/login_button_height"
                    android:layout_marginTop="@dimen/spacing_xl"
                    android:text="@string/btn_login" />

                <!-- 进度条容器 -->
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/spacing_md">

                    <ProgressBar
                        android:id="@+id/progressBar"
                        style="?android:attr/progressBarStyleSmall"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:indeterminateTint="@color/primary"
                        android:visibility="gone"
                        tools:visibility="visible" />

                </FrameLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 错误信息显示 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardError"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/login_card_margin"
            android:layout_marginTop="@dimen/spacing_md"
            android:layout_marginEnd="@dimen/login_card_margin"
            android:visibility="gone"
            app:cardBackgroundColor="@color/error"
            app:cardCornerRadius="@dimen/corner_radius_md"
            app:cardElevation="@dimen/elevation_sm"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardLogin"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvError"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/spacing_md"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="登录失败，请检查用户名和密码" />

        </androidx.cardview.widget.CardView>

        <!-- 底部间距 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/spacing_xxl"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardError" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
