package com.example.gxzhaiwu

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.gxzhaiwu.ui.login.LoginFragment
import com.example.gxzhaiwu.utils.PreferencesManager

class MainActivity : AppCompatActivity() {

    private lateinit var preferencesManager: PreferencesManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)

        preferencesManager = PreferencesManager.getInstance(this)

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        // 检查用户登录状态
        if (savedInstanceState == null) {
            if (!preferencesManager.isLoggedIn()) {
                // 用户未登录，显示登录界面
                showLoginFragment()
            } else {
                // 用户已登录，显示主界面
                // TODO: 显示主界面
                showLoginFragment() // 临时显示登录界面
            }
        }
    }

    /**
     * 显示登录界面
     */
    private fun showLoginFragment() {
        supportFragmentManager.beginTransaction()
            .replace(R.id.main, LoginFragment())
            .commit()
    }
}