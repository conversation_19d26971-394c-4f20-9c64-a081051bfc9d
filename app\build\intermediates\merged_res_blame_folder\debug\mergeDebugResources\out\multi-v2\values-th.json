{"logs": [{"outputFile": "com.example.gxzhaiwu.app-mergeDebugResources-38:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\54318c60d4cc5aa50171bceead402775\\transformed\\material-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1044,1114,1204,1274,1334,1421,1487,1552,1613,1677,1738,1792,1893,1954,2014,2068,2138,2249,2336,2413,2500,2582,2663,2806,2885,2967,3099,3191,3269,3323,3376,3442,3512,3590,3661,3741,3813,3891,3960,4029,4127,4209,4297,4390,4484,4558,4627,4722,4774,4857,4925,5010,5098,5160,5224,5287,5357,5457,5553,5650,5743,5801,5858,5935,6017,6092", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "280,353,425,508,593,679,778,891,971,1039,1109,1199,1269,1329,1416,1482,1547,1608,1672,1733,1787,1888,1949,2009,2063,2133,2244,2331,2408,2495,2577,2658,2801,2880,2962,3094,3186,3264,3318,3371,3437,3507,3585,3656,3736,3808,3886,3955,4024,4122,4204,4292,4385,4479,4553,4622,4717,4769,4852,4920,5005,5093,5155,5219,5282,5352,5452,5548,5645,5738,5796,5853,5930,6012,6087,6163"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3277,4078,4177,4290,4370,4438,4508,4598,4668,4728,4815,4881,4946,5007,5071,5132,5186,5287,5348,5408,5462,5532,5643,5730,5807,5894,5976,6057,6200,6279,6361,6493,6585,6663,6717,6770,6836,6906,6984,7055,7135,7207,7285,7354,7423,7521,7603,7691,7784,7878,7952,8021,8116,8168,8251,8319,8404,8492,8554,8618,8681,8751,8851,8947,9044,9137,9195,9252,9411,9493,9568", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "330,3032,3104,3187,3272,3358,4172,4285,4365,4433,4503,4593,4663,4723,4810,4876,4941,5002,5066,5127,5181,5282,5343,5403,5457,5527,5638,5725,5802,5889,5971,6052,6195,6274,6356,6488,6580,6658,6712,6765,6831,6901,6979,7050,7130,7202,7280,7349,7418,7516,7598,7686,7779,7873,7947,8016,8111,8163,8246,8314,8399,8487,8549,8613,8676,8746,8846,8942,9039,9132,9190,9247,9324,9488,9563,9639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9f721d637f67d4db61062e1122557cba\\transformed\\appcompat-1.7.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,9329", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,9406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\deb0beff519b9c4f8f9fad521546f280\\transformed\\core-1.16.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,3459,3562,3660,3758,3861,3966,9644", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3454,3557,3655,3753,3856,3961,4073,9740"}}]}]}