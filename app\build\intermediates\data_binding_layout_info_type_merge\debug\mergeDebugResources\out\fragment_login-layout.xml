<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_login" modulePackage="com.example.gxzhaiwu" filePath="app\src\main\res\layout\fragment_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_login_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="189" endOffset="12"/></Target><Target id="@+id/ivLogo" view="ImageView"><Expressions/><location startLine="15" startOffset="8" endLine="23" endOffset="55"/></Target><Target id="@+id/tvAppTitle" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="37" endOffset="62"/></Target><Target id="@+id/tvSubtitle" view="TextView"><Expressions/><location startLine="39" startOffset="8" endLine="50" endOffset="66"/></Target><Target id="@+id/cardLogin" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="53" startOffset="8" endLine="148" endOffset="43"/></Target><Target id="@+id/tilLogin" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="74" startOffset="16" endLine="91" endOffset="71"/></Target><Target id="@+id/etLogin" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="82" startOffset="20" endLine="89" endOffset="49"/></Target><Target id="@+id/tilPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="94" startOffset="16" endLine="113" endOffset="71"/></Target><Target id="@+id/etPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="104" startOffset="20" endLine="111" endOffset="49"/></Target><Target id="@+id/btnLogin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="116" startOffset="16" endLine="126" endOffset="64"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="134" startOffset="20" endLine="142" endOffset="52"/></Target><Target id="@+id/cardError" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="151" startOffset="8" endLine="177" endOffset="43"/></Target><Target id="@+id/tvError" view="TextView"><Expressions/><location startLine="167" startOffset="12" endLine="175" endOffset="45"/></Target></Targets></Layout>