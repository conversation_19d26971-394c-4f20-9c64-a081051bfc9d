<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_login" modulePackage="com.example.gxzhaiwu" filePath="app\src\main\res\layout\fragment_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_login_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="133" endOffset="12"/></Target><Target id="@+id/ivLogo" view="ImageView"><Expressions/><location startLine="14" startOffset="8" endLine="22" endOffset="55"/></Target><Target id="@+id/tvAppTitle" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="35" endOffset="62"/></Target><Target id="@+id/tvSubtitle" view="TextView"><Expressions/><location startLine="37" startOffset="8" endLine="47" endOffset="66"/></Target><Target id="@+id/tilLogin" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="50" startOffset="8" endLine="68" endOffset="63"/></Target><Target id="@+id/etLogin" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="61" startOffset="12" endLine="66" endOffset="38"/></Target><Target id="@+id/tilPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="70" startOffset="8" endLine="89" endOffset="63"/></Target><Target id="@+id/etPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="82" startOffset="12" endLine="87" endOffset="38"/></Target><Target id="@+id/btnLogin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="92" startOffset="8" endLine="101" endOffset="67"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="104" startOffset="8" endLine="113" endOffset="40"/></Target><Target id="@+id/tvError" view="TextView"><Expressions/><location startLine="116" startOffset="8" endLine="129" endOffset="40"/></Target></Targets></Layout>