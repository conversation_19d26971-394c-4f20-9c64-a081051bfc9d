<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_login" modulePackage="com.example.gxzhaiwu" filePath="app\src\main\res\layout\fragment_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_login_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="187" endOffset="12"/></Target><Target id="@+id/ivLogo" view="ImageView"><Expressions/><location startLine="16" startOffset="8" endLine="24" endOffset="55"/></Target><Target id="@+id/tvAppTitle" view="TextView"><Expressions/><location startLine="26" startOffset="8" endLine="35" endOffset="62"/></Target><Target id="@+id/tvSubtitle" view="TextView"><Expressions/><location startLine="37" startOffset="8" endLine="46" endOffset="66"/></Target><Target id="@+id/cardLogin" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="49" startOffset="8" endLine="146" endOffset="43"/></Target><Target id="@+id/tilLogin" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="68" startOffset="16" endLine="89" endOffset="71"/></Target><Target id="@+id/etLogin" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="80" startOffset="20" endLine="87" endOffset="49"/></Target><Target id="@+id/tilPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="92" startOffset="16" endLine="115" endOffset="71"/></Target><Target id="@+id/etPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="106" startOffset="20" endLine="113" endOffset="49"/></Target><Target id="@+id/btnLogin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="118" startOffset="16" endLine="124" endOffset="54"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="132" startOffset="20" endLine="140" endOffset="52"/></Target><Target id="@+id/cardError" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="149" startOffset="8" endLine="175" endOffset="43"/></Target><Target id="@+id/tvError" view="TextView"><Expressions/><location startLine="165" startOffset="12" endLine="173" endOffset="45"/></Target></Targets></Layout>