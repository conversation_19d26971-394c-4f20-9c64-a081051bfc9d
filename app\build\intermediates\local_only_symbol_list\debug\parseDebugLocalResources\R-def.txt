R_DEF: Internal format may change without notice
local
color accent
color background_gradient_end
color background_gradient_start
color background_primary
color background_secondary
color black
color error
color info
color outline
color primary
color primary_dark
color primary_light
color success
color surface
color surface_variant
color text_error
color text_hint
color text_primary
color text_secondary
color warning
color white
dimen corner_radius_lg
dimen corner_radius_md
dimen corner_radius_sm
dimen corner_radius_xl
dimen elevation_lg
dimen elevation_md
dimen elevation_sm
dimen login_button_height
dimen login_card_margin
dimen login_card_padding
dimen login_input_spacing
dimen login_logo_size
dimen spacing_lg
dimen spacing_md
dimen spacing_sm
dimen spacing_xl
dimen spacing_xs
dimen spacing_xxl
drawable bg_button_primary
drawable bg_gradient_primary
drawable bg_login_card
drawable ic_launcher_background
drawable ic_launcher_foreground
id btnLogin
id cardError
id cardLogin
id etLogin
id etPassword
id ivLogo
id main
id progressBar
id tilLogin
id tilPassword
id tvAppTitle
id tvError
id tvSubtitle
layout activity_main
layout fragment_login
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string btn_login
string error_empty_login
string error_empty_password
string error_invalid_email
string error_login_failed
string error_login_too_short
string error_network_unavailable
string error_password_too_short
string hint_login
string hint_password
string login_subtitle
string login_title
string success_login
style Base.Theme.GxZhaiWu
style LoginButton
style LoginCard
style LoginSubtitle
style LoginTextInputLayout
style LoginTitle
style Theme.GxZhaiWu
xml backup_rules
xml data_extraction_rules
xml network_security_config
