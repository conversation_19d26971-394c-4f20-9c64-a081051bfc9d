<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#FF4081</color>
    <color name="background_gradient_end">#FFFFFF</color>
    <color name="background_gradient_start">#E3F2FD</color>
    <color name="background_primary">#F5F5F5</color>
    <color name="background_secondary">#FFFFFF</color>
    <color name="black">#FF000000</color>
    <color name="error">#F44336</color>
    <color name="info">#2196F3</color>
    <color name="outline">#E0E0E0</color>
    <color name="primary">#2196F3</color>
    <color name="primary_dark">#1976D2</color>
    <color name="primary_light">#BBDEFB</color>
    <color name="success">#4CAF50</color>
    <color name="surface">#FFFFFF</color>
    <color name="surface_variant">#F8F9FA</color>
    <color name="text_error">#F44336</color>
    <color name="text_hint">#BDBDBD</color>
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="warning">#FF9800</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="corner_radius_lg">12dp</dimen>
    <dimen name="corner_radius_md">8dp</dimen>
    <dimen name="corner_radius_sm">4dp</dimen>
    <dimen name="corner_radius_xl">16dp</dimen>
    <dimen name="elevation_lg">8dp</dimen>
    <dimen name="elevation_md">4dp</dimen>
    <dimen name="elevation_sm">2dp</dimen>
    <dimen name="login_button_height">56dp</dimen>
    <dimen name="login_card_margin">24dp</dimen>
    <dimen name="login_card_padding">32dp</dimen>
    <dimen name="login_input_spacing">20dp</dimen>
    <dimen name="login_logo_size">100dp</dimen>
    <dimen name="spacing_lg">24dp</dimen>
    <dimen name="spacing_md">16dp</dimen>
    <dimen name="spacing_sm">8dp</dimen>
    <dimen name="spacing_xl">32dp</dimen>
    <dimen name="spacing_xs">4dp</dimen>
    <dimen name="spacing_xxl">48dp</dimen>
    <string name="app_name">债务管理系统</string>
    <string name="btn_login">登录</string>
    <string name="error_empty_login">请输入用户名或邮箱</string>
    <string name="error_empty_password">请输入密码</string>
    <string name="error_invalid_email">请输入有效的邮箱地址</string>
    <string name="error_login_failed">登录失败，请重试</string>
    <string name="error_login_too_short">用户名长度不能少于2位</string>
    <string name="error_network_unavailable">网络连接不可用，请检查网络设置</string>
    <string name="error_password_too_short">密码长度不能少于6位</string>
    <string name="hint_login">用户名或邮箱</string>
    <string name="hint_password">密码</string>
    <string name="login_subtitle">请登录您的账户</string>
    <string name="login_title">债务管理系统</string>
    <string name="success_login">登录成功，欢迎使用！</string>
    <style name="Base.Theme.GxZhaiWu" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="LoginButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">@dimen/corner_radius_lg</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="rippleColor">@color/primary_light</item>
    </style>
    <style name="LoginCard" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">@dimen/corner_radius_xl</item>
        <item name="cardElevation">@dimen/elevation_md</item>
        <item name="cardBackgroundColor">@color/surface</item>
    </style>
    <style name="LoginSubtitle">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="LoginTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="boxStrokeWidth">2dp</item>
        <item name="hintTextColor">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="startIconTint">@color/primary</item>
        <item name="endIconTint">@color/primary</item>
    </style>
    <style name="LoginTitle">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">28sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="Theme.GxZhaiWu" parent="Base.Theme.GxZhaiWu"/>
</resources>